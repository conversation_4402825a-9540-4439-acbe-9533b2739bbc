// Quick test for external image functionality
const testData = {
  "title": "Test External Image Post",
  "slug": "test-external-image",
  "content": "# Test Post\n\nThis is a test post with external image.",
  "categories": ["tech"],
  "meta": {
    "title": "Test External Image",
    "description": "Testing external image functionality."
  },
  "publishedAt": "2025-01-08T00:00:00.000Z",
  "_status": "published",
  "summary": "Test post with external image",
  "featuredImage": {
    "type": "external",
    "data": "https://picsum.photos/800/600?random=1",
    "alt": "Test external image"
  }
}

console.log('Test data for external image:')
console.log(JSON.stringify(testData, null, 2))

// Test the API endpoint
fetch('http://localhost:3000/api/v1/posts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(testData)
})
.then(response => response.json())
.then(data => {
  console.log('API Response:')
  console.log(JSON.stringify(data, null, 2))
})
.catch(error => {
  console.error('Error:', error)
})
