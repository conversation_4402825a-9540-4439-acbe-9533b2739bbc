/**
 * Utility functions for handling fallback images from Unsplash
 */

// Collection of fixed, reliable Unsplash image URLs for different purposes
export const unsplashImages = {
  // Hero images (larger, landscape format)
  hero: [
    'https://images.unsplash.com/photo-1504711434969-e33886168f5c?q=80&w=1600&h=900&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1495020689067-958852a7765e?q=80&w=1600&h=900&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1557683316-973673baf926?q=80&w=1600&h=900&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1585829365295-ab7cd400c167?q=80&w=1600&h=900&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1493612276216-ee3925520721?q=80&w=1600&h=900&auto=format&fit=crop',
  ],

  // Category card images (medium size)
  category: [
    'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1517649763962-0c623066013b?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1505751172876-fa1923c5c528?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1532094349884-543bc11b234d?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1547891654-e66ed7ebb968?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1603739903239-8b6e64c3b185?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1476514525535-07fb3b4ae5f1?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1551218808-94e220e084d2?q=80&w=800&h=600&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?q=80&w=800&h=600&auto=format&fit=crop',
  ],

  // Sidebar/thumbnail images (smaller)
  thumbnail: [
    'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=400&h=300&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1517649763962-0c623066013b?q=80&w=400&h=300&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?q=80&w=400&h=300&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1505751172876-fa1923c5c528?q=80&w=400&h=300&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1532094349884-543bc11b234d?q=80&w=400&h=300&auto=format&fit=crop',
  ],
}

// Default image to use as a final fallback
const DEFAULT_IMAGE =
  'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=800&h=600&auto=format&fit=crop'

/**
 * Get a fallback image from the specified category
 * @param category - The category of image to get ('hero', 'category', or 'thumbnail')
 * @param index - Optional index to get a specific image (useful for consistent images in loops)
 * @returns A URL string for an Unsplash image
 */
export const getFallbackImage = (
  category: keyof typeof unsplashImages = 'category',
  index?: number,
): string => {
  const imageArray = unsplashImages[category]

  // Return default image if the category doesn't exist or is empty
  if (!imageArray || imageArray.length === 0) {
    return DEFAULT_IMAGE
  }

  if (typeof index === 'number') {
    // Use modulo to ensure the index is within bounds
    return imageArray[index % imageArray.length] || DEFAULT_IMAGE
  }

  // Return a random image from the specified category
  return imageArray[Math.floor(Math.random() * imageArray.length)] || DEFAULT_IMAGE
}

/**
 * Default fallback image to use when no other options are specified
 */
export const defaultFallbackImage = unsplashImages.category[0] || DEFAULT_IMAGE
