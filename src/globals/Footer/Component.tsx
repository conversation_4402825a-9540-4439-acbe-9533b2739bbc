import { getCachedGlobal } from '@/utilities/getGlobals'
import Link from 'next/link'
import React from 'react'

import type { Footer } from '@/payload-types'

import { ThemeSelector } from '@/providers/Theme/ThemeSelector'
import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'

export async function Footer() {
  const footerData: Footer = await getCachedGlobal('footer', 1)()

  const navItems = footerData?.navItems || []

  const categoryItems = [
    {
      label: '政治新聞',
      href: '/categories/politics',
    },
    {
      label: '經濟財經',
      href: '/categories/economy',
    },
    {
      label: '社會新聞',
      href: '/categories/society',
    },
    {
      label: '國際新聞',
      href: '/categories/international',
    },
    {
      label: '科技資訊',
      href: '/categories/technology',
    },
  ]

  return (
    <footer className="mt-auto bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info Section */}
          <div className="col-span-2 lg:col-span-1">
            <Link className="flex items-center mb-4" href="/">
              <Logo />
            </Link>
            <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
              專業新聞媒體平台，致力於提供最即時、最準確的新聞資訊，為讀者帶來深度報導與獨到見解。
            </p>
            <div className="flex space-x-4">
              <Link
                href="/rss"
                className="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <span className="sr-only">RSS訂閱</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3.429 2.571c8.63 0 15.714 7.084 15.714 15.714h-3.571c0-6.72-5.423-12.143-12.143-12.143v-3.571z" />
                  <path d="M3.429 8.571c3.955 0 7.143 3.188 7.143 7.143h-3.571c0-1.955-1.616-3.571-3.571-3.571v-3.571z" />
                  <circle cx="5.714" cy="14.286" r="1.714" />
                </svg>
              </Link>
              <Link
                href="/newsletter"
                className="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <span className="sr-only">電子報</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </Link>
            </div>
          </div>

          {/* News Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4 border-b border-gray-300 dark:border-gray-700 pb-2">
              新聞分類
            </h3>
            <ul className="space-y-2">
              {categoryItems.map((item) => (
                <li key={item.label}>
                  <Link
                    href={item.href}
                    className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* About & Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4 border-b border-gray-300 dark:border-gray-700 pb-2">
              關於我們
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  公司簡介
                </Link>
              </li>
              <li>
                <Link
                  href="/editorial-team"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  編輯團隊
                </Link>
              </li>
              <li>
                <Link
                  href="/press-release"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  新聞稿發布
                </Link>
              </li>
              <li>
                <Link
                  href="/advertising"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  廣告合作
                </Link>
              </li>
              <li>
                <Link
                  href="/careers"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  人才招募
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  聯絡我們
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact & Legal */}
          <div>
            <h3 className="text-lg font-semibold mb-4 border-b border-gray-300 dark:border-gray-700 pb-2">
              聯絡資訊
            </h3>
            <div className="space-y-3 text-gray-600 dark:text-gray-300">
              <div className="flex items-start space-x-2">
                <svg className="w-4 h-4 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm">澳門特別行政區新口岸友誼大馬路555號</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                <span className="text-sm">(853) 2888 9999</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium mb-2">法律聲明</h4>
              <ul className="space-y-1 text-sm">
                <li>
                  <Link
                    href="/privacy"
                    className="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    隱私政策
                  </Link>
                </li>
                <li>
                  <Link
                    href="/terms"
                    className="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    使用條款
                  </Link>
                </li>
                <li>
                  <Link
                    href="/ethics"
                    className="text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    新聞倫理
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* CMS Navigation Items */}
        {/* {navItems.length > 0 && (
          <div className="mt-8 pt-8 border-t border-gray-300 dark:border-gray-700">
            <h3 className="text-lg font-semibold mb-4">其他連結</h3>
            <nav className="flex flex-wrap gap-4">
              {navItems.map(({ link }, i) => (
                <CMSLink
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  key={i}
                  {...link}
                />
              ))}
            </nav>
          </div>
        )} */}
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-300 dark:border-gray-700 bg-gray-200 dark:bg-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                © {new Date().getFullYear()} 新聞媒體網站. 版權所有.
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <ThemeSelector />
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <span>追蹤我們：</span>
                <Link
                  href="/facebook"
                  className="hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Facebook
                </Link>
                <span>•</span>
                <Link
                  href="/twitter"
                  className="hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Twitter
                </Link>
                <span>•</span>
                <Link
                  href="/youtube"
                  className="hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  YouTube
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
