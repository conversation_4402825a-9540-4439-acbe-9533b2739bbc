import type { GlobalAfterChangeHook } from 'payload'

import { revalidateTag } from 'next/cache'
import { getGlobalCacheTag } from '@/utilities/getGlobals'

export const revalidateFooter: GlobalAfterChangeHook = ({ doc, req: { payload, context } }) => {
  if (!context.disableRevalidate) {
    payload.logger.info(`Revalidating footer`)

    revalidateTag(getGlobalCacheTag('footer'))
  }

  return doc
}
