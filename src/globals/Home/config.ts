import { GlobalConfig } from 'payload'
import { anyone } from '@/access/anyone'

export const Home: GlobalConfig = {
  slug: 'home',
  access: {
    read: anyone,
  },
  fields: [
    {
      name: 'sections',
      type: 'array',
      label: '欄位',
      minRows: 6,
      maxRows: 6,
      // required: true,
      admin: {},
      fields: [
        {
          name: 'title',
          label: '標題',
          type: 'text',
          required: true,
          admin: { description: 'The title of the section' },
        },
        {
          name: 'category',
          label: '目錄',
          type: 'relationship',
          relationTo: 'categories',
          hasMany: false,
          required: true,
          admin: { description: 'The category of the section' },
        },
      ],
    },
  ],
}
