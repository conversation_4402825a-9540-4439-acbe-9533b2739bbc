'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Menu, X } from 'lucide-react'
import { usePathname } from 'next/navigation'

import type { Header as HeaderType } from '@/payload-types'

import { CMSLink } from '@/components/Link'

export const HeaderNav: React.FC<{ data: HeaderType }> = ({ data }) => {
  const navItems = data?.navItems || []
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()

  // Function to create navigation links with consistent styling
  const renderNavLink = (link: any, i: number, isMobile: boolean = false) => {
    const isActive =
      link.type === 'reference' && link.reference?.value
        ? pathname.includes(link.reference.value?.slug?.toString())
        : link.type === 'custom' && pathname === link.url

    // For mobile menu item, we'll close the menu after clicking
    const handleClick = isMobile ? () => setMobileMenuOpen(false) : undefined

    if (isMobile) {
      return (
        <div key={i} onClick={handleClick}>
          <CMSLink
            {...link}
            appearance="link"
            className={`text-base font-medium ${
              isActive ? 'text-blue-800 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300'
            }`}
          />
        </div>
      )
    }

    return (
      <div key={i} className="relative group">
        <CMSLink
          {...link}
          appearance="none"
          className={`font-medium text-sm px-4 py-6 rounded-none transition text-gray-800 dark:text-gray-200 ${
            isActive
              ? 'bg-blue-500 dark:bg-blue-700 text-white'
              : 'hover:bg-blue-500/80 hover:text-white'
          }`}
        />
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Desktop Navigation */}
      <nav className="hidden md:flex justify-between items-center">
        <div className="flex items-center">
          {navItems.map(({ link }, i) => renderNavLink(link, i))}
        </div>
      </nav>

      {/* Mobile Navigation Button */}
      <div className="md:hidden flex justify-between items-center">
        <button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="p-1 text-white"
          aria-label="Toggle mobile menu"
        >
          {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="absolute top-full left-0 right-0 bg-white dark:bg-slate-800 shadow-lg p-4 z-50 md:hidden">
          <nav className="flex flex-col space-y-4">
            {navItems.map(({ link }, i) => renderNavLink(link, i, true))}
          </nav>
        </div>
      )}
    </div>
  )
}
