'use client'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import { SearchIcon } from 'lucide-react'

import type { Header } from '@/payload-types'

import { Logo } from '@/components/Logo/Logo'
import { HeaderNav } from './Nav'

interface HeaderClientProps {
  data: Header
}

export const HeaderClient: React.FC<HeaderClientProps> = ({ data }) => {
  /* Storing the value in a useState to avoid hydration errors */
  const [theme, setTheme] = useState<string | null>(null)
  const { headerTheme, setHeaderTheme } = useHeaderTheme()
  const pathname = usePathname()

  useEffect(() => {
    setHeaderTheme(null)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname])

  useEffect(() => {
    if (headerTheme && headerTheme !== theme) setTheme(headerTheme)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [headerTheme])

  return (
    <header
      className="w-full sticky top-0 z-30 rounded-b-md overflow-hidden shadow-md"
      {...(theme ? { 'data-theme': theme } : {})}
    >
      {/* Top row - Logo, search and other elements */}
      <div className="bg-white dark:bg-gray-900 relative">
        {/* Subtle pattern overlay */}
        {/* <div className="absolute inset-0 opacity-5"></div> */}

        <div className="container mx-auto py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center gap-2">
              {/* Logo */}
              <h1 className="text-2xl md:text-3xl font-bold font-serif text-gray-800 dark:text-white">
                澳門新聞
              </h1>
              <span className="hidden md:inline text-sm font-light text-gray-600 dark:text-gray-300">
                MACAU NEWS
              </span>
            </Link>

            <div className="flex items-center gap-4">
              {/* Search bar */}
              <form action="/search" method="get" className="relative hidden md:block">
                <input
                  type="text"
                  name="q"
                  placeholder="搜尋新聞..."
                  className="bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full py-1 px-4 text-sm focus:outline-none focus:ring-2 focus:ring-blue-400 w-[200px] text-gray-700 dark:text-gray-200 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  aria-label="Search"
                >
                  <SearchIcon className="w-4 h-4 text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors" />
                </button>
              </form>

              {/* Mobile search icon */}
              <Link href="/search" className="md:hidden p-1 text-gray-700 dark:text-gray-300">
                <span className="sr-only">Search</span>
                <SearchIcon className="w-5 h-5" />
              </Link>

              {/* Current date */}
              <div className="hidden md:block text-sm text-gray-600 dark:text-gray-300">
                {new Date().toLocaleDateString('zh-HK', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom row - Navigation links */}
      {/* <div className="bg-gradient-to-r from-blue-500/80 to-blue-400/80 dark:from-blue-700/80 dark:to-blue-600/80 backdrop-blur-lg"> */}
      <div className="bg-gradient-to-r bg-neutral-100 dark:bg-gray-800">
        <div className="container mx-auto">
          <HeaderNav data={data} />
        </div>
      </div>
    </header>
  )
}
