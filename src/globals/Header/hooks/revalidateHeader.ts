import type { GlobalAfterChangeHook } from 'payload'

import { revalidateTag } from 'next/cache'
import { getGlobalCacheTag } from '@/utilities/getGlobals'

export const revalidateHeader: GlobalAfterChangeHook = ({ doc, req: { payload, context } }) => {
  if (process.env.IS_SCRIPT) return doc

  if (!context.disableRevalidate) {
    payload.logger.info(`Revalidating header`)

    revalidateTag(getGlobalCacheTag('header'))
  }

  return doc
}
