# Post Creation API - Quick Start Guide

## 🚀 Quick Start

The API uses a single endpoint that intelligently handles both single posts and batch requests.

**Endpoint:** `POST /api/v1/posts`

### 1. Single Post Creation

Send a post object directly:

**Example Request:**
```json
{
  "title": "My News Article",
  "slug": "my-news-article",
  "content": "# Article Title\n\nArticle content in markdown...",
  "summary": "Brief summary of the article",
  "source_url": "https://original-source.com/article",
  "source_site_name": "Original Source",
  "categories": ["technology", "news"],
  "featuredImage": {
    "type": "base64",
    "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "filename": "article-image.jpg",
    "alt": "Article featured image"
  },
  "_status": "published"
}
```

**Response:**
```json
{
  "success": true,
  "post": {
    "id": 123,
    "title": "My News Article",
    "slug": "my-news-article"
  }
}
```

### 2. Batch Post Creation

Send an object with a `posts` array:

**Example Request:**
```json
{
  "posts": [
    {
      "title": "Article 1",
      "slug": "article-1",
      "content": "Content for article 1...",
      "summary": "Summary 1",
      "categories": ["tech"],
      "featuredImage": {
        "type": "external",
        "data": "https://example.com/image1.jpg",
        "alt": "Article 1 image"
      }
    },
    {
      "title": "Article 2", 
      "slug": "article-2",
      "content": "Content for article 2...",
      "summary": "Summary 2",
      "categories": ["news"]
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully created 2 posts",
  "totalRequested": 2,
  "succeeded": 2,
  "failed": 0,
  "results": [
    {
      "id": 124,
      "title": "Article 1",
      "slug": "article-1",
      "success": true
    },
    {
      "id": 125,
      "title": "Article 2",
      "slug": "article-2", 
      "success": true
    }
  ]
}
```

## 🔑 Authentication

All requests require a Bearer token:

```bash
curl -X POST /api/v1/posts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer press-site-api-token-12345" \
  -d '{"title":"Test","slug":"test","content":"Test content"}'
```

## 🖼️ Image Options

### Option 1: Base64 Upload
```json
{
  "featuredImage": {
    "type": "base64",
    "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "alt": "Image description"
  }
}
```
*Note: Filename is automatically generated from the MIME type in the data URL*

### Option 2: External URL
```json
{
  "featuredImage": {
    "type": "external",
    "data": "https://example.com/image.jpg",
    "alt": "Image description"
  }
}
```

### Option 3: No Image
Simply omit the `featuredImage` field.

## 📋 Required Fields

**Minimum required fields:**
- `title` (string)
- `slug` (string) 
- `content` (string, markdown)

**Optional fields:**
- `summary` (string)
- `source_url` (string, URL)
- `source_site_name` (string)
- `extra` (object, arbitrary JSON)
- `categories` (array of strings)
- `meta.title` (string)
- `meta.description` (string)
- `publishedAt` (ISO datetime string)
- `_status` ("draft" | "published", defaults to "draft")
- `featuredImage` (object, see image options above)

## ⚡ Performance Tips

### For High Volume (100+ articles):
1. **Use batch endpoint** with 10-20 posts per request
2. **Add delays** between batches (1 second recommended)
3. **Handle errors gracefully** - retry failed posts individually
4. **Monitor response times** and adjust batch size accordingly

### Example Batch Processing:
```javascript
const processBatch = async (articles, batchSize = 15) => {
  for (let i = 0; i < articles.length; i += batchSize) {
    const batch = articles.slice(i, i + batchSize)
    
    const response = await fetch('/api/v1/posts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer press-site-api-token-12345'
      },
      body: JSON.stringify({ posts: batch })
    })
    
    const result = await response.json()
    console.log(`Batch ${Math.floor(i/batchSize) + 1}: ${result.succeeded} succeeded, ${result.failed} failed`)
    
    // Optional delay between batches
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}
```

## 🔍 Error Handling

### Single Post Errors:
```json
{
  "success": false,
  "error": "Filename is required for base64 images"
}
```

### Batch Post Errors:
```json
{
  "success": true,
  "message": "Processed with some failures: 2 succeeded, 1 failed",
  "totalRequested": 3,
  "succeeded": 2,
  "failed": 1,
  "results": [
    {
      "id": 124,
      "title": "Article 1",
      "slug": "article-1",
      "success": true
    },
    {
      "title": "Article 2",
      "slug": "article-2",
      "success": false,
      "error": "Image upload error: Invalid base64 data"
    }
  ]
}
```

## 📞 Support

For issues or questions:
1. Check the implementation summary: `src/docs/IMPLEMENTATION_SUMMARY.md`
2. Review test examples: `src/tests/post-creation.test.ts`
3. Verify Azure configuration and environment variables
