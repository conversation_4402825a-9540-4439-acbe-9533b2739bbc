/**
 * Test utilities for Azure Blob Storage functions
 */

import { 
  generateUniqueFilename, 
  getContentTypeFromFilename, 
  base64ToBuffer 
} from '@/lib/azure-blob'

// Test data
const testBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
const testFilename = "test-image.png"

export const testAzureBlobUtilities = () => {
  console.log('🧪 Testing Azure Blob Storage utilities...\n')
  
  // Test 1: Generate unique filename
  console.log('1. Testing generateUniqueFilename:')
  const uniqueFilename1 = generateUniqueFilename(testFilename)
  const uniqueFilename2 = generateUniqueFilename(testFilename)
  
  console.log(`   Original: ${testFilename}`)
  console.log(`   Unique 1: ${uniqueFilename1}`)
  console.log(`   Unique 2: ${uniqueFilename2}`)
  console.log(`   ✅ Filenames are unique: ${uniqueFilename1 !== uniqueFilename2}`)
  
  // Test 2: Get content type from filename
  console.log('\n2. Testing getContentTypeFromFilename:')
  const testFiles = [
    'image.jpg',
    'photo.jpeg', 
    'graphic.png',
    'animation.gif',
    'modern.webp',
    'vector.svg',
    'unknown.xyz'
  ]
  
  testFiles.forEach(filename => {
    const contentType = getContentTypeFromFilename(filename)
    console.log(`   ${filename} -> ${contentType}`)
  })
  
  // Test 3: Base64 to buffer conversion
  console.log('\n3. Testing base64ToBuffer:')
  try {
    const buffer = base64ToBuffer(testBase64)
    console.log(`   Base64 length: ${testBase64.length}`)
    console.log(`   Buffer length: ${buffer.length}`)
    console.log(`   ✅ Conversion successful`)
    
    // Test with clean base64 (no data URL prefix)
    const cleanBase64 = testBase64.replace(/^data:image\/[a-z]+;base64,/, '')
    const buffer2 = base64ToBuffer(cleanBase64)
    console.log(`   Clean base64 length: ${cleanBase64.length}`)
    console.log(`   Buffer2 length: ${buffer2.length}`)
    console.log(`   ✅ Both conversions produce same result: ${buffer.equals(buffer2)}`)
    
  } catch (error) {
    console.log(`   ❌ Base64 conversion failed: ${error}`)
  }
  
  console.log('\n🎉 Azure Blob utilities test completed!')
}

// Test the utilities if this file is run directly
if (require.main === module) {
  testAzureBlobUtilities()
}
