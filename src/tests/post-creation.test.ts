/**
 * Test file for Post Creation API with Image Upload
 *
 * This file contains example requests and expected responses for testing
 * the enhanced post creation API with Azure Blob Storage integration.
 */

// Example single post request with base64 image
export const singlePostWithBase64Image = {
  title: 'Test Post with Base64 Image',
  slug: 'test-post-base64',
  content: '# Test Content\n\nThis is a test post with a base64 image.',
  summary: 'A test post for validating base64 image upload functionality',
  source_url: 'https://example.com/source',
  source_site_name: 'Example News',
  extra: {
    author: 'Test Author',
    tags: ['test', 'api', 'image'],
  },
  categories: ['technology', 'testing'],
  meta: {
    title: 'Test Post Meta Title',
    description: 'Test post meta description',
  },
  _status: 'draft' as const,
  featuredImage: {
    type: 'base64' as const,
    data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    alt: 'Test image',
  },
}

// Example single post request with external image URL
export const singlePostWithExternalImage = {
  title: 'Test Post with External Image',
  slug: 'test-post-external',
  content: '# Test Content\n\nThis is a test post with an external image.',
  summary: 'A test post for validating external image URL functionality',
  source_url: 'https://example.com/source',
  source_site_name: 'Example News',
  categories: ['technology'],
  meta: {
    title: 'Test Post External Meta Title',
    description: 'Test post external meta description',
  },
  _status: 'published' as const,
  featuredImage: {
    type: 'external' as const,
    data: 'https://example.com/image.jpg',
    alt: 'External test image',
  },
}

// Example batch request with mixed image types
export const batchPostsRequest = {
  posts: [
    {
      title: 'Batch Post 1 - Base64',
      slug: 'batch-post-1',
      content: '# Batch Post 1\n\nFirst post in batch with base64 image.',
      summary: 'First batch post',
      categories: ['batch-test'],
      featuredImage: {
        type: 'base64' as const,
        data: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',

        alt: 'Batch post 1 image',
      },
    },
    {
      title: 'Batch Post 2 - External',
      slug: 'batch-post-2',
      content: '# Batch Post 2\n\nSecond post in batch with external image.',
      summary: 'Second batch post',
      categories: ['batch-test'],
      featuredImage: {
        type: 'external' as const,
        data: 'https://example.com/batch-2.jpg',
        alt: 'Batch post 2 image',
      },
    },
    {
      title: 'Batch Post 3 - No Image',
      slug: 'batch-post-3',
      content: '# Batch Post 3\n\nThird post in batch without image.',
      summary: 'Third batch post',
      categories: ['batch-test'],
    },
  ],
}

// Example test function (not executable, just for reference)
export const testPostCreation = async () => {
  const AUTH_TOKEN = 'press-site-api-token-12345'

  console.log('Testing single post creation with base64 image...')

  try {
    const response = await fetch('/api/v1/posts/single', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify(singlePostWithBase64Image),
    })

    const result = await response.json()
    console.log('Single post result:', result)

    if (result.success) {
      console.log('✅ Single post creation successful')
    } else {
      console.log('❌ Single post creation failed:', result.error)
    }
  } catch (error) {
    console.log('❌ Single post creation error:', error)
  }

  console.log('\nTesting batch post creation...')

  try {
    const response = await fetch('/api/v1/posts/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify(batchPostsRequest),
    })

    const result = await response.json()
    console.log('Batch post result:', result)

    if (result.success) {
      console.log('✅ Batch post creation successful')
      console.log(`Created ${result.succeeded} posts, ${result.failed} failed`)
    } else {
      console.log('❌ Batch post creation failed:', result.error)
    }
  } catch (error) {
    console.log('❌ Batch post creation error:', error)
  }
}

// Expected response schemas for reference
export const expectedSinglePostResponse = {
  success: true,
  post: {
    id: 123,
    title: 'Test Post with Base64 Image',
    slug: 'test-post-base64',
  },
}

export const expectedBatchPostResponse = {
  success: true,
  message: 'Successfully created 3 posts',
  totalRequested: 3,
  succeeded: 3,
  failed: 0,
  results: [
    {
      id: 124,
      title: 'Batch Post 1 - Base64',
      slug: 'batch-post-1',
      success: true,
    },
    {
      id: 125,
      title: 'Batch Post 2 - External',
      slug: 'batch-post-2',
      success: true,
    },
    {
      id: 126,
      title: 'Batch Post 3 - No Image',
      slug: 'batch-post-3',
      success: true,
    },
  ],
}
