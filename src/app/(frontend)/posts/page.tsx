import type { <PERSON>ada<PERSON> } from 'next/types'

import { PageRange } from '@/components/PageRange'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import PageClient from './page.client'
import { PostCard } from '@/components/Post/PostCard'
import { Post } from '@/payload-types'
import Link from 'next/link'
import Image from 'next/image'
import { getFallbackImage } from '@/utilities/getFallbackImage'

type Args = {
  searchParams: Promise<{
    page?: string
  }>
}

export default async function Page({ searchParams }: Args) {
  const { page: pageParam } = await searchParams
  const currentPage = Number(pageParam) || 1
  const pageSize = 19

  const payload = await getPayload({ config: configPromise })

  const posts = await payload.find({
    collection: 'posts',
    depth: 1,
    limit: pageSize,
    page: currentPage,
    pagination: true,
    where: {
      _status: {
        equals: 'published',
      },
    },
    select: {
      title: true,
      slug: true,
      categories: true,
      meta: true,
      publishedAt: true,
      featuredImage: true,
    },
    sort: '-publishedAt',
  })

  // Ensure page properties are defined with defaults
  const page = posts.page || 1
  const totalPages = posts.totalPages || 1

  return (
    <div className="pt-0 pb-12">
      <PageClient />

      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-slate-700 to-slate-500 pt-24 pb-16 mb-10">
        <div className="absolute inset-0 opacity-30">
          <Image
            src={getFallbackImage('hero')}
            alt="category"
            fill
            className="object-cover"
            priority
          />
        </div>
        <div className="container mx-auto relative z-10">
          <div className="text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">所有新聞文章</h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              瀏覽我們的最新新聞報導、深度分析和獨家內容
            </p>

            {/* Page range info */}
            {posts.totalDocs > 0 && (
              <div className="mt-6 text-white/70 text-sm">找到 {posts.totalDocs} 篇文章</div>
            )}
          </div>
        </div>
      </div>

      {/* Page range info */}
      <div className="container mx-auto mb-8">
        <PageRange
          collection="posts"
          currentPage={page}
          limit={pageSize}
          totalDocs={posts.totalDocs}
        />
      </div>

      {posts.docs.length > 0 ? (
        <div className="container mx-auto">
          {/* Featured posts with new layout */}
          {posts.docs.length > 0 && (
            <div className="mb-10">
              <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">精選文章</h2>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left column - Single large featured post (2/3 width) */}
                <div className="lg:col-span-2">
                  {posts.docs[0] && <PostCard post={posts.docs[0] as Post} variant="featured" />}
                </div>

                {posts.docs.length < 4 ? (
                  <div className="lg:col-span-1 h-full flex flex-col">
                    <div className="space-y-4 flex flex-col h-full">
                      {posts.docs.slice(1, 4).map((post) => (
                        <PostCard key={post.id} post={post as Post} variant="featured" />
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4 flex flex-col h-full">
                    {posts.docs.slice(1, 4).map((post) => (
                      <PostCard key={post.id} post={post as Post} variant="sidebar" />
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Secondary posts in a grid */}
          {posts.docs.length > 4 && (
            <div className="mb-10">
              <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">最新報導</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-56">
                {posts.docs.slice(4, 7).map((post) => (
                  <PostCard key={post.id} post={post as Post} variant="overlay" imgHeight="h-56" />
                ))}
              </div>
            </div>
          )}

          {/* Remaining posts */}
          {posts.docs.length > 7 && (
            <div className="mb-10">
              <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">更多文章</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {posts.docs.slice(7).map((post) => (
                  <PostCard key={post.id} post={post as Post} variant="minimal" imgHeight="h-40" />
                ))}
              </div>
            </div>
          )}

          {/* Pagination - Fixed for proper linkage */}
          {totalPages > 1 && (
            <div className="mt-12">
              <Pagination>
                <PaginationContent>
                  {page > 1 && (
                    <PaginationItem>
                      <Link href={`/posts?page=${page - 1}`} legacyBehavior>
                        <PaginationPrevious />
                      </Link>
                    </PaginationItem>
                  )}

                  {/* First page */}
                  <PaginationItem>
                    <Link href="/posts?page=1" legacyBehavior>
                      <PaginationLink isActive={page === 1}>1</PaginationLink>
                    </Link>
                  </PaginationItem>

                  {/* Show ellipsis if needed */}
                  {page > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Page numbers logic */}
                  {Array.from({ length: totalPages }).map((_, i) => {
                    const pageNumber = i + 1
                    // Only show page numbers near current page (within 1 page on either side)
                    if (
                      pageNumber !== 1 &&
                      pageNumber !== totalPages &&
                      Math.abs(pageNumber - page) <= 1
                    ) {
                      return (
                        <PaginationItem key={pageNumber}>
                          <Link href={`/posts?page=${pageNumber}`} legacyBehavior>
                            <PaginationLink isActive={page === pageNumber}>
                              {pageNumber}
                            </PaginationLink>
                          </Link>
                        </PaginationItem>
                      )
                    }
                    return null
                  })}

                  {/* Show ellipsis if needed */}
                  {page < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Last page - only show if more than 1 page */}
                  {totalPages > 1 && (
                    <PaginationItem>
                      <Link href={`/posts?page=${totalPages}`} legacyBehavior>
                        <PaginationLink isActive={page === totalPages}>{totalPages}</PaginationLink>
                      </Link>
                    </PaginationItem>
                  )}

                  {page < totalPages && (
                    <PaginationItem>
                      <Link href={`/posts?page=${page + 1}`} legacyBehavior>
                        <PaginationNext />
                      </Link>
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      ) : (
        <div className="container mx-auto text-center py-12">
          <p className="text-gray-500">目前沒有任何文章。</p>
        </div>
      )}
    </div>
  )
}

export async function generateMetadata({ searchParams }: Args): Promise<Metadata> {
  const { page: pageParam } = await searchParams
  return {
    title: `所有新聞 | 澳門新聞`,
  }
}
