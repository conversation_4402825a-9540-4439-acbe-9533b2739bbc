import type { Metadata } from 'next'
import { getGlobal } from '@/utilities/getGlobals'
import { getPayload } from 'payload'
import configPromise from '@/payload.config'
import type { Home, Post } from '@/payload-types'
import { PostCard } from '@/components/Post/PostCard'

export const metadata: Metadata = {
  title: '澳門新聞',
  description: '澳門新聞',
}

export default async function Home() {
  // Fetch global data and initialize payload
  const home = (await getGlobal('home')) as Home
  const payload = await getPayload({ config: configPromise })

  // Section post limits
  const defaultLimits = [1, 2, 2, 4, 4, 5]

  // Fetch posts for each section
  const sections = await Promise.all(
    home?.sections?.map(async (sec, idx: number) => {
      const limit = defaultLimits[idx] || 1
      const postsRes = await payload.find({
        collection: 'posts',
        limit,
        sort: '-publishedAt',
        depth: 1,
        where: {
          categories: {
            equals: sec.category,
          },
          _status: {
            equals: 'published',
          },
        },
      })
      return {
        title: sec.title,
        posts: postsRes.docs as Post[],
      }
    }) || [],
  )

  return (
    <main className="container mx-auto px-4 py-8">
      {/* First Row: 50% | 25% | 25% layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-10">
        {/* Section 1: 50% width */}
        {sections[0] && sections[0].posts.length > 0 && (
          <section className="lg:col-span-1">
            <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">
              {sections[0].title}
            </h2>
            {sections[0].posts[0] && <PostCard post={sections[0].posts[0]} variant="featured" />}
          </section>
        )}

        {/* Section 2 & 3: Each 25% width (50% on mobile) */}
        <div className="lg:col-span-1 grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Section 2 */}
          {sections[1] && sections[1].posts.length > 0 && (
            <section className="sm:col-span-1 flex flex-col h-full">
              <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">
                {sections[1].title}
              </h2>
              <div className="min-h-[400px] md:min-h-none grid grid-cols-1 gap-4 flex-grow">
                {sections[1].posts.map((post: Post) => (
                  <PostCard key={post.id} post={post} variant="overlay" />
                ))}
              </div>
            </section>
          )}

          {/* Section 3 */}
          {sections[2] && sections[2].posts.length > 0 && (
            <section className="sm:col-span-1 flex flex-col h-full">
              <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">
                {sections[2].title}
              </h2>
              <div className="min-h-[400px] md:min-h-none grid grid-cols-1 gap-4 flex-grow">
                {sections[2].posts.map((post: Post) => (
                  <PostCard key={post.id} post={post} variant="overlay" />
                ))}
              </div>
            </section>
          )}
        </div>
      </div>

      {/* Second Row: Section 4 full width */}
      {sections[3] && sections[3].posts.length > 0 && (
        <section className="mb-10">
          <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">
            {sections[3].title}
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            {sections[3].posts.map((post: Post) => (
              <PostCard key={post.id} post={post} variant="minimal" imgHeight="h-40" />
            ))}
          </div>
        </section>
      )}

      {/* Third Row: 2/3 for section 5, 1/3 for section 6 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Section 5 - 2/3 width */}
        {sections[4] && sections[4].posts.length > 0 && (
          <section className="lg:col-span-2">
            <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">
              {sections[4].title}
            </h2>
            <div className="grid grid-cols-1 gap-4">
              {/* Featured post (first post) */}
              {sections[4].posts[0] && <PostCard post={sections[4].posts[0]} variant="featured" />}

              {/* Other posts in a 3-column grid */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {sections[4].posts.slice(1).map((post: Post) => (
                  <PostCard key={post.id} post={post} imgHeight="h-36" />
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Section 6 - 1/3 width */}
        {sections[5] && sections[5].posts.length > 0 && (
          <section className="lg:col-span-1 h-full flex flex-col">
            <h2 className="text-2xl font-bold mb-6 border-l-4 border-blue-600 pl-3">
              {sections[5].title}
            </h2>
            <div className="space-y-4 flex flex-col h-full">
              {sections[5].posts.map((post: Post) => (
                <PostCard key={post.id} post={post} variant="sidebar" />
              ))}
            </div>
          </section>
        )}
      </div>
    </main>
  )
}
