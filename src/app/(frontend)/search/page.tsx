import type { <PERSON>ada<PERSON> } from 'next/types'

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import { Search } from '@/search/Component'
import PageClient from './page.client'
import { PostCard } from '@/components/Post/PostCard'
import { Post } from '@/payload-types'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import { PageRange } from '@/components/PageRange'
import Link from 'next/link'

type Args = {
  searchParams: Promise<{
    q?: string
    page?: string
  }>
}

export default async function Page({ searchParams }: Args) {
  const { q: query = '', page: pageParam } = await searchParams
  const currentPage = Number(pageParam) || 1
  const pageSize = 12

  const payload = await getPayload({ config: configPromise })

  const posts = await payload.find({
    collection: 'search',
    depth: 1,
    limit: pageSize,
    page: currentPage,
    select: {
      title: true,
      slug: true,
      categories: true,
      meta: true,
      publishedAt: true,
      featuredImage: true,
    },
    pagination: true,
    ...(query
      ? {
          where: {
            or: [
              {
                title: {
                  like: query,
                },
              },
              {
                'meta.description': {
                  like: query,
                },
              },
              {
                'meta.title': {
                  like: query,
                },
              },
              {
                slug: {
                  like: query,
                },
              },
            ],
          },
        }
      : {}),
  })

  // Ensure page properties are defined with defaults
  const page = posts.page || 1
  const totalPages = posts.totalPages || 1

  return (
    <div className="pt-0 pb-12">
      <PageClient />

      {/* Hero Section with Background and Search */}
      <div className="relative bg-gradient-to-r from-blue-600 to-blue-400 pt-24 pb-16 mb-10">
        <div className="absolute inset-0 bg-blue-500/20 backdrop-blur-sm"></div>
        {/* Pattern overlay */}
        <div className="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSIjZmZmZmZmIj48cGF0aCBkPSJNMzYgMzRjMCAxIDEgMiAyIDJoMnYtNGgtMmMtMSAwLTIgMS0yIDJ6TTMwIDM0YzAgMSAxIDIgMiAyaDJ2LTRoLTJjLTEgMC0yIDEtMiAyek0yNCAzNGMwIDEgMSAyIDIgMmgydi00aC0yYy0xIDAtMiAxLTIgMnpNMTggMzRjMCAxIDEgMiAyIDJoMnYtNGgtMmMtMSAwLTIgMS0yIDJ6TTEyIDM0YzAgMSAxIDIgMiAyaDJ2LTRoLTJjLTEgMC0yIDEtMiAyek02IDM0YzAgMSAxIDIgMiAyaDJ2LTRIOGMtMSAwLTIgMS0yIDJ6TTYgMjhjMCAxIDEgMiAyIDJoMnYtNEg4Yy0xIDAtMiAxLTIgMnpNNiAyMmMwIDEgMSAyIDIgMmgydi00SDhjLTEgMC0yIDEtMiAyek02IDE2YzAgMSAxIDIgMiAyaDJ2LTRIOGMtMSAwLTIgMS0yIDJ6TTYgMTBjMCAxIDEgMiAyIDJoMlY4SDhjLTEgMC0yIDEtMiAyek02IDRjMCAxIDEgMiAyIDJoMlYySDhjLTEgMC0yIDEtMiAyek00OCA0YzAgMSAxIDIgMiAyaDJWMmgtMmMtMSAwLTIgMS0yIDJ6TTQyIDRjMCAxIDEgMiAyIDJoMlYyaC0yYy0xIDAtMiAxLTIgMnpNMzYgNGMwIDEgMSAyIDIgMmgyVjJoLTJjLTEgMC0yIDEtMiAyek0zMCA0YzAgMSAxIDIgMiAyaDJWMmgtMmMtMSAwLTIgMS0yIDJ6TTI0IDRjMCAxIDEgMiAyIDJoMlYyaC0yYy0xIDAtMiAxLTIgMnpNMTggNGMwIDEgMSAyIDIgMmgyVjJoLTJjLTEgMC0yIDEtMiAyek0xMiA0YzAgMSAxIDIgMiAyaDJWMmgtMmMtMSAwLTIgMS0yIDJ6TTU0IDJjLTEgMC0yIDEtMiAydjRoNFYyaC0yek01NCAxNGg0VjhoLTR2NnpNNTQgMjBoNHYtNmgtNHY2ek01NCAyNmg0di02aC00djZ6TTU0IDMyaDR2LTZoLTR2NnpNNTQgMzhoNHYtNmgtNHY2ek01NCA0NGMxIDAgMi0xIDItMnYtNGgtNHY2aDJ6TTQ4IDQ0YzEgMCAyLTEgMi0yaC00djJoMnpNNDIgNDRjMSAwIDItMSAyLTJoLTR2Mmgyek0zNiA0NGMxIDAgMi0xIDItMmgtNHYyaDJ6TTMwIDQ0YzEgMCAyLTEgMi0yaC00djJoMnpNMjQgNDRjMSAwIDItMSAyLTJoLTR2Mmgyek0xOCA0NGMxIDAgMi0xIDItMmgtNHYyaDJ6TTEyIDQ0YzEgMCAyLTEgMi0yaC00djJoMnpNOCA0NGg0di02SDh2NGMwIDEgMSAyIDIgMnoiPjwvcGF0aD48L2c+PC9zdmc+')]"></div>

        <div className="container mx-auto relative z-10">
          <div className="text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-white mb-6">
              {query ? `"${query}" 的搜尋結果` : '搜尋新聞'}
            </h1>

            <div className="max-w-xl mx-auto bg-white/10 backdrop-blur-md p-6 rounded-lg shadow-lg">
              <Search initialValue={query} />
            </div>

            {query && (
              <p className="text-white mt-4 text-sm">
                {posts.docs.length > 0
                  ? `找到 ${posts.totalDocs} 條相關新聞`
                  : `沒有找到相關的 "${query}" 新聞`}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Results Section */}
      {posts.docs.length > 0 ? (
        <div className="container mx-auto">
          {/* Page range info */}
          {posts.totalDocs > pageSize && (
            <div className="mb-8">
              <PageRange
                collection="posts"
                currentPage={page}
                limit={pageSize}
                totalDocs={posts.totalDocs}
              />
            </div>
          )}

          {/* First result as featured */}
          {posts.docs.length > 0 && (
            <div className="mb-10">
              <PostCard post={posts.docs[0] as Post} variant="featured" />
            </div>
          )}

          {/* Secondary results in a grid */}
          {posts.docs.length > 1 && (
            <>
              <h2 className="text-xl font-bold mb-6 text-gray-700">更多相關文章</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
                {posts.docs.slice(1, 4).map((post) => (
                  <PostCard key={post.id} post={post as Post} variant="overlay" imgHeight="h-56" />
                ))}
              </div>
            </>
          )}

          {/* Remaining results */}
          {posts.docs.length > 4 && (
            <>
              <h2 className="text-xl font-bold mb-6 text-gray-700">其他文章</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {posts.docs.slice(4).map((post) => (
                  <PostCard key={post.id} post={post as Post} variant="minimal" imgHeight="h-40" />
                ))}
              </div>
            </>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-12">
              <Pagination>
                <PaginationContent>
                  {page > 1 && (
                    <PaginationItem>
                      <Link href={`/search?q=${query}&page=${page - 1}`} legacyBehavior>
                        <PaginationPrevious />
                      </Link>
                    </PaginationItem>
                  )}

                  {/* First page */}
                  <PaginationItem>
                    <Link href={`/search?q=${query}&page=1`} legacyBehavior>
                      <PaginationLink isActive={page === 1}>1</PaginationLink>
                    </Link>
                  </PaginationItem>

                  {/* Show ellipsis if needed */}
                  {page > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Page numbers logic */}
                  {Array.from({ length: totalPages }).map((_, i) => {
                    const pageNumber = i + 1
                    // Only show page numbers near current page (within 1 page on either side)
                    if (
                      pageNumber !== 1 &&
                      pageNumber !== totalPages &&
                      Math.abs(pageNumber - page) <= 1
                    ) {
                      return (
                        <PaginationItem key={pageNumber}>
                          <Link href={`/search?q=${query}&page=${pageNumber}`} legacyBehavior>
                            <PaginationLink isActive={page === pageNumber}>
                              {pageNumber}
                            </PaginationLink>
                          </Link>
                        </PaginationItem>
                      )
                    }
                    return null
                  })}

                  {/* Show ellipsis if needed */}
                  {page < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Last page - only show if more than 1 page */}
                  {totalPages > 1 && (
                    <PaginationItem>
                      <Link href={`/search?q=${query}&page=${totalPages}`} legacyBehavior>
                        <PaginationLink isActive={page === totalPages}>{totalPages}</PaginationLink>
                      </Link>
                    </PaginationItem>
                  )}

                  {page < totalPages && (
                    <PaginationItem>
                      <Link href={`/search?q=${query}&page=${page + 1}`} legacyBehavior>
                        <PaginationNext />
                      </Link>
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      ) : (
        <div className="container mx-auto text-center py-12">
          {!query && <p className="text-gray-500">請輸入關鍵字進行搜尋。</p>}
        </div>
      )}
    </div>
  )
}

export async function generateMetadata({ searchParams }: Args): Promise<Metadata> {
  const { q: query } = await searchParams
  return {
    title: query ? `搜尋 "${query}" 的結果 | 澳門新聞` : `搜尋 | 澳門新聞`,
  }
}
