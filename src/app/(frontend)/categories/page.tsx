import type { Metada<PERSON> } from 'next/types'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ationContent,
  <PERSON><PERSON>ationI<PERSON>,
  <PERSON><PERSON>ationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import PageClient from './page.client'
import Link from 'next/link'
import Image from 'next/image'
import { getFallbackImage } from '@/utilities/getFallbackImage'

type Args = {
  searchParams: Promise<{
    page?: string
  }>
}

// Define our simplified category type for display
type CategoryDisplay = {
  id: string | number
  title: string
  slug: string
  description?: string
}

export default async function Page({ searchParams }: Args) {
  const { page: pageParam } = await searchParams
  const currentPage = Number(pageParam) || 1
  const pageSize = 12

  const payload = await getPayload({ config: configPromise })

  // Fetch all categories
  const categoriesData = await payload.find({
    collection: 'categories',
    depth: 1,
    limit: 100, // Get all categories
    pagination: true,
  })

  // Convert payload categories to our display type
  const categories: CategoryDisplay[] = categoriesData.docs.map((category) => ({
    id: category.id,
    title: category.title || 'Untitled Category',
    slug: category.slug || '',
    // description: category.description || undefined,
  }))

  // Ensure page properties are defined with defaults
  const page = categoriesData.page || 1
  const totalPages = categoriesData.totalPages || 1

  // Get a hero image from our utility
  const fixedHeroImage = getFallbackImage('hero')

  return (
    <div className="pt-0 pb-12">
      <PageClient />

      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-slate-700 to-slate-500 pt-24 pb-16 mb-10">
        <div className="absolute inset-0 bg-blue-500/30 backdrop-blur-sm z-10"></div>
        <div className="absolute inset-0 opacity-30">
          <Image src={fixedHeroImage} alt="category" fill className="object-cover" priority />
        </div>

        <div className="container mx-auto relative z-10">
          <div className="text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">文章分類</h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              瀏覽我們的主題分類，尋找您感興趣的新聞內容
            </p>

            {/* Categories count */}
            {categories.length > 0 && (
              <div className="mt-6 text-white/70 text-sm">共 {categories.length} 個分類</div>
            )}
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      {categories.length > 0 ? (
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <Link
                href={`/categories/${category.slug}`}
                key={category.id.toString()}
                className="block group"
              >
                <div className="relative h-48 w-full overflow-hidden rounded-lg shadow-lg">
                  {/* Gradient overlay from left to right */}
                  <div className="absolute inset-0 bg-gradient-to-r from-slate-600 via-blue-900/60 to-transparent z-10 backdrop-blur-sm"></div>

                  {/* Category image - right aligned */}
                  <div className="absolute inset-0">
                    <Image
                      src={getFallbackImage('category', index)}
                      alt={category.title}
                      fill
                      className="object-cover object-right transform group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>

                  {/* Category content - left aligned */}
                  <div className="absolute left-0 top-0 bottom-0 right-0 z-20 flex items-center">
                    <div className="p-6 max-w-[65%]">
                      <h2 className="text-2xl font-bold text-white mb-2">{category.title}</h2>
                      {category.description && (
                        <p className="text-white/80 text-sm line-clamp-2">{category.description}</p>
                      )}
                      <div className="mt-4 inline-block">
                        <span className="text-white text-sm font-medium border-b border-white/50 pb-1 group-hover:border-white transition-colors">
                          查看更多
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Pagination if needed */}
          {totalPages > 1 && (
            <div className="mt-12">
              <Pagination>
                <PaginationContent>
                  {page > 1 && (
                    <PaginationItem>
                      <Link href={`/categories?page=${page - 1}`} legacyBehavior>
                        <PaginationPrevious />
                      </Link>
                    </PaginationItem>
                  )}

                  {/* First page */}
                  <PaginationItem>
                    <Link href="/categories?page=1" legacyBehavior>
                      <PaginationLink isActive={page === 1}>1</PaginationLink>
                    </Link>
                  </PaginationItem>

                  {/* Show ellipsis if needed */}
                  {page > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Page numbers logic */}
                  {Array.from({ length: totalPages }).map((_, i) => {
                    const pageNumber = i + 1
                    // Only show page numbers near current page (within 1 page on either side)
                    if (
                      pageNumber !== 1 &&
                      pageNumber !== totalPages &&
                      Math.abs(pageNumber - page) <= 1
                    ) {
                      return (
                        <PaginationItem key={pageNumber}>
                          <Link href={`/categories?page=${pageNumber}`} legacyBehavior>
                            <PaginationLink isActive={page === pageNumber}>
                              {pageNumber}
                            </PaginationLink>
                          </Link>
                        </PaginationItem>
                      )
                    }
                    return null
                  })}

                  {/* Show ellipsis if needed */}
                  {page < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}

                  {/* Last page - only show if more than 1 page */}
                  {totalPages > 1 && (
                    <PaginationItem>
                      <Link href={`/categories?page=${totalPages}`} legacyBehavior>
                        <PaginationLink isActive={page === totalPages}>{totalPages}</PaginationLink>
                      </Link>
                    </PaginationItem>
                  )}

                  {page < totalPages && (
                    <PaginationItem>
                      <Link href={`/categories?page=${page + 1}`} legacyBehavior>
                        <PaginationNext />
                      </Link>
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      ) : (
        <div className="container mx-auto text-center py-12">
          <p className="text-gray-500">目前沒有任何分類。</p>
        </div>
      )}
    </div>
  )
}

export async function generateMetadata({ searchParams }: Args): Promise<Metadata> {
  return {
    title: `文章分類 | 澳門新聞`,
  }
}
