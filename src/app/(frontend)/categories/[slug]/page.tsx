import type { <PERSON>ada<PERSON> } from 'next'

import { PayloadRedirects } from '@/components/PayloadRedirects'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { draftMode } from 'next/headers'
import React, { cache } from 'react'
import Link from 'next/link'
import Image from 'next/image'

import type { Post, Category as PayloadCategory } from '@/payload-types'

import { generateMeta } from '@/utilities/generateMeta'
import { PostCard } from '@/components/Post/PostCard'
import { getFallbackImage } from '@/utilities/getFallbackImage'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination'
import { PageRange } from '@/components/PageRange'
import PageClient from './page.client'

// Extended Category type with optional description
type ExtendedCategory = PayloadCategory & {
  description?: string | null
}

// Use our utility for fixed image URLs
const fixedHeroImage = getFallbackImage('hero')

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })
  const categories = await payload.find({
    collection: 'categories',
    draft: false,
    limit: 1000,
    overrideAccess: false,
    pagination: false,
    select: {
      slug: true,
    },
  })

  const params = categories.docs.map(({ slug }) => {
    return { slug }
  })

  return params
}

type Args = {
  params: Promise<{
    slug?: string
  }>
  searchParams: Promise<{
    page?: string
  }>
}

export default async function CategoryPage({ params: paramsPromise, searchParams }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '' } = await paramsPromise
  const { page: pageParam } = await searchParams
  const currentPage = Number(pageParam) || 1
  const limit = 9

  const url = '/categories/' + slug
  const category = (await queryCategoryBySlug({ slug })) as ExtendedCategory

  if (!category) return <PayloadRedirects url={url} />

  const payload = await getPayload({ config: configPromise })
  // Fetch posts for this category
  const postsData = await payload.find({
    collection: 'posts',
    draft,
    limit,
    page: currentPage,
    depth: 1,
    where: {
      categories: {
        contains: category.id,
      },
      _status: {
        equals: 'published',
      },
    },
    sort: '-publishedAt',
  })

  // Fetch all other categories for the sidebar
  const allCategoriesData = await payload.find({
    collection: 'categories',
    limit: 100,
    depth: 0,
    where: {
      slug: {
        not_equals: slug,
      },
    },
  })
  const otherCategories = allCategoriesData.docs as ExtendedCategory[]

  // Ensure page properties have default values
  const page = postsData.page || 1
  const totalPages = postsData.totalPages || 1
  const totalDocs = postsData.totalDocs || 0

  return (
    <div className="pt-0 pb-12">
      <PageClient />

      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-slate-700 to-slate-500 pt-24 pb-16 mb-10">
        <div className="absolute inset-0 bg-blue-500/30 backdrop-blur-sm z-10"></div>
        <div className="absolute inset-0 opacity-30">
          <Image src={fixedHeroImage} alt={category.title} fill className="object-cover" priority />
        </div>

        <div className="container mx-auto relative z-10">
          <div className="text-center">
            <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">{category.title}</h1>
            {category.description && (
              <p className="text-white/80 text-lg max-w-2xl mx-auto">{category.description}</p>
            )}
            <div className="mt-4">
              <Link
                href="/categories"
                className="inline-block text-white/80 hover:text-white text-sm font-medium border-b border-white/50 pb-1 hover:border-white transition-colors"
              >
                ← 返回所有分類
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto">
        <div className="lg:flex lg:gap-8">
          {/* Left Column: Posts */}
          <div className="lg:w-2/3">
            <div className="mb-6">
              <PageRange
                collection="posts"
                currentPage={page}
                limit={limit}
                totalDocs={totalDocs}
              />
            </div>

            {postsData && postsData.docs.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                  {postsData.docs.map((post) => (
                    <PostCard
                      key={post.id}
                      post={post as Post}
                      variant="minimal"
                      imgHeight="h-48"
                    />
                  ))}
                </div>

                {totalPages > 1 && (
                  <div className="mt-12">
                    <Pagination>
                      <PaginationContent>
                        {page > 1 && (
                          <PaginationItem>
                            <Link href={`/categories/${slug}?page=${page - 1}`} legacyBehavior>
                              <PaginationPrevious />
                            </Link>
                          </PaginationItem>
                        )}
                        {Array.from({ length: totalPages }).map((_, i) => {
                          const pageNumber = i + 1
                          if (
                            pageNumber === 1 ||
                            pageNumber === totalPages ||
                            (pageNumber >= page - 1 && pageNumber <= page + 1)
                          ) {
                            return (
                              <PaginationItem key={pageNumber}>
                                <Link
                                  href={`/categories/${slug}?page=${pageNumber}`}
                                  legacyBehavior
                                >
                                  <PaginationLink isActive={page === pageNumber}>
                                    {pageNumber}
                                  </PaginationLink>
                                </Link>
                              </PaginationItem>
                            )
                          } else if (pageNumber === page - 2 || pageNumber === page + 2) {
                            return <PaginationEllipsis key={pageNumber} />
                          }
                          return null
                        })}
                        {page < totalPages && (
                          <PaginationItem>
                            <Link href={`/categories/${slug}?page=${page + 1}`} legacyBehavior>
                              <PaginationNext />
                            </Link>
                          </PaginationItem>
                        )}
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">目前此分類沒有任何文章。</p>
              </div>
            )}
          </div>

          {/* Right Column: Other Categories (Sticky) */}
          <div className="lg:w-1/3 lg:sticky lg:top-24 h-fit mt-10 lg:mt-0">
            <h2 className="text-xl font-semibold mb-4 border-l-4 border-blue-600 pl-3">其他分類</h2>
            <div className="space-y-4">
              {otherCategories.map((otherCat, index) => {
                // Get a fallback image using the index for consistency
                const imageSrc = getFallbackImage('thumbnail', index)
                return (
                  <Link
                    href={`/categories/${otherCat.slug}`}
                    key={otherCat.id}
                    className="block group"
                  >
                    <div className="relative rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden h-24">
                      <Image
                        src={imageSrc}
                        alt={otherCat.title}
                        fill
                        className="object-cover transform group-hover:scale-105 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent backdrop-blur-sm flex items-end p-4 z-10">
                        <h3 className="text-white text-lg font-semibold group-hover:text-blue-200 transition-colors">
                          {otherCat.title}
                        </h3>
                      </div>
                    </div>
                  </Link>
                )
              })}
              {otherCategories.length === 0 && (
                <p className="text-gray-500">沒有其他分類可顯示。</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '' } = await paramsPromise
  const category = (await queryCategoryBySlug({ slug })) as ExtendedCategory

  return generateMeta({
    doc: category,
  })
}

const queryCategoryBySlug = cache(async ({ slug }: { slug: string }) => {
  const { isEnabled: draft } = await draftMode()
  const payload = await getPayload({ config: configPromise })
  const result = await payload.find({
    collection: 'categories',
    draft,
    limit: 1,
    overrideAccess: draft,
    pagination: false,
    depth: 1,
    where: {
      slug: {
        equals: slug,
      },
    },
  })
  return result.docs?.[0] || null
})
