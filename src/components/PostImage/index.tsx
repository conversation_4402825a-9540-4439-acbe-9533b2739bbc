'use client'

import Image from 'next/image'
import { Media } from '@/components/Media'
import type { Media as MediaType, Post } from '@/payload-types'
import { useState } from 'react'
import { getFallbackImage } from '@/utilities/getFallbackImage'
import { cn } from '@/utilities/ui'

interface PostImageProps {
  image?: MediaType | number | Post['featuredImage'] | null
  alt?: string
  className?: string
  imgClassName?: string
  priority?: boolean
  fallbackSrc?: string
  fill?: boolean
  fallbackCategory?: 'hero' | 'category' | 'thumbnail'
}

export const PostImage = ({
  image,
  alt = '',
  className = '',
  imgClassName = '',
  priority = false,
  fallbackSrc,
  fill = false,
  fallbackCategory = 'category',
}: PostImageProps) => {
  const [imgError, setImgError] = useState(false)

  // Use the provided fallbackSrc or get one from our utility
  const fallbackImage = fallbackSrc || getFallbackImage(fallbackCategory)

  if (imgError || !image) {
    return (
      <div className={cn('relative w-full h-full', className)}>
        <Image
          src={fallbackImage}
          alt={alt}
          className={imgClassName}
          width={fill ? undefined : 800}
          height={fill ? undefined : 600}
          priority={priority}
          fill={fill}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          onError={() => setImgError(true)}
        />
      </div>
    )
  }

  // Handle new featuredImage structure
  if (typeof image === 'object' && image !== null && 'type' in image) {
    const featuredImage = image as Post['featuredImage']

    if (featuredImage?.type === 'external' && featuredImage.url) {
      // Handle external URL - pass the URL as a string resource
      // The ImageMedia component now handles string resources properly
      return (
        <Media
          className={className}
          imgClassName={imgClassName}
          resource={featuredImage.url}
          alt={featuredImage.alt || alt}
          priority={priority}
          fill={fill}
          onLoad={() => setImgError(false)}
        />
      )
    } else if (featuredImage?.type === 'upload' && featuredImage.media) {
      // Handle uploaded media
      return (
        <Media
          className={className}
          imgClassName={imgClassName}
          resource={featuredImage.media}
          alt={featuredImage.alt || alt}
          priority={priority}
          fill={fill}
          onLoad={() => setImgError(false)}
        />
      )
    }
  }

  // Handle legacy media object or ID (only if it's not the new featuredImage structure)
  if (typeof image === 'object' && image !== null && !('type' in image)) {
    // This is a legacy Media object
    const mediaResource = image as MediaType
    return (
      <Media
        className={className}
        imgClassName={imgClassName}
        resource={mediaResource}
        alt={alt}
        priority={priority}
        fill={fill}
        onLoad={() => setImgError(false)}
      />
    )
  } else if (typeof image === 'number') {
    // This is a media ID
    return (
      <Media
        className={className}
        imgClassName={imgClassName}
        resource={image}
        alt={alt}
        priority={priority}
        fill={fill}
        onLoad={() => setImgError(false)}
      />
    )
  }

  // If we get here, show fallback
  return (
    <div className={cn('relative w-full h-full', className)}>
      <Image
        src={fallbackImage}
        alt={alt}
        className={imgClassName}
        width={fill ? undefined : 800}
        height={fill ? undefined : 600}
        priority={priority}
        fill={fill}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        onError={() => setImgError(true)}
      />
    </div>
  )
}
