import { Post } from '@/payload-types'
import { PostImage } from '@/components/PostImage'
import Link from 'next/link'
import { ReactNode } from 'react'

interface PostCardProps {
  post: Post
  variant?: 'default' | 'overlay' | 'featured' | 'minimal' | 'sidebar'
  imgHeight?: string
}

export function PostCard({ post, variant = 'default', imgHeight = 'h-48' }: PostCardProps) {
  // Format date for display
  const formatDate = (date: string | null | undefined) => {
    if (!date) return ''
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // Common classes for all variants
  const baseCardClasses =
    'overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100'

  // Wrap with Link if post has slug
  const PostWrapper = ({ children }: { children: ReactNode }) => {
    if (post.slug) {
      return (
        <Link href={`/posts/${post.slug}`} className="block h-full">
          {children}
        </Link>
      )
    }
    return <div>{children}</div>
  }

  switch (variant) {
    // Full overlay variant (for hero cards)
    case 'featured':
      return (
        <PostWrapper key={post.id}>
          <article className={`${baseCardClasses} relative group h-fit`}>
            <PostImage
              image={post.featuredImage}
              alt={post.title}
              imgClassName="w-full h-[400px] object-cover transition-transform duration-700 group-hover:scale-105"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/40 to-transparent flex flex-col justify-end p-6">
              <h3 className="text-white text-2xl font-semibold mb-2 hover:text-blue-200 transition-colors duration-300">
                {post.title}
              </h3>
              <time className="text-gray-200 text-sm">{formatDate(post.publishedAt)}</time>
            </div>
          </article>
        </PostWrapper>
      )

    // Text overlay variant (for section 2 & 3 cards)
    case 'overlay':
      return (
        <PostWrapper key={post.id}>
          <article className={`${baseCardClasses} relative group h-full flex flex-col`}>
            <div className="relative flex-grow">
              <PostImage
                image={post.featuredImage}
                alt={post.title}
                imgClassName="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                fill
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent/20 flex flex-col justify-end p-4">
                <h3 className="text-white text-base font-semibold line-clamp-2 group-hover:text-blue-200 transition-colors duration-300">
                  {post.title}
                </h3>
                <time className="text-gray-200 text-xs mt-1">{formatDate(post.publishedAt)}</time>
              </div>
            </div>
          </article>
        </PostWrapper>
      )

    // Clean minimal cards (for section 4)
    case 'minimal':
      return (
        <PostWrapper key={post.id}>
          <article className={`${baseCardClasses} group flex flex-col h-full`}>
            <div className="relative overflow-hidden">
              <PostImage
                image={post.featuredImage}
                alt={post.title}
                imgClassName={`w-full ${imgHeight} object-cover transition-transform duration-500 group-hover:scale-105`}
              />
            </div>
            <div className="p-4 flex-grow flex flex-col bg-neutral-100">
              <h3 className="font-semibold text-sm mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                {post.title}
              </h3>
              <time className="text-gray-500 text-xs mt-auto">{formatDate(post.publishedAt)}</time>
            </div>
          </article>
        </PostWrapper>
      )

    // Sidebar variant with side-by-side image and no padding
    case 'sidebar':
      return (
        <PostWrapper key={post.id}>
          <article className="flex rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 group overflow-hidden h-full">
            <div className="relative overflow-hidden w-1/3 flex-shrink-0">
              <PostImage
                image={post.featuredImage}
                alt={post.title}
                imgClassName="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                fill
              />
            </div>
            <div className="flex flex-col flex-grow p-4 bg-neutral-100">
              <h3 className="font-medium line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                {post.title}
              </h3>
              <time className="text-gray-500 text-xs mt-auto">{formatDate(post.publishedAt)}</time>
            </div>
          </article>
        </PostWrapper>
      )

    // Default card variant
    default:
      return (
        <PostWrapper key={post.id}>
          <article className={`${baseCardClasses} group flex flex-col h-full`}>
            <div className="relative overflow-hidden">
              <PostImage
                image={post.featuredImage}
                alt={post.title}
                imgClassName={`w-full ${imgHeight} object-cover transition-transform duration-500 group-hover:scale-105`}
              />
            </div>
            <div className="p-4 flex-grow flex flex-col bg-neutral-100">
              <h3 className="font-semibold text-base mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
                {post.title}
              </h3>
              <time className="text-gray-500 text-xs mt-auto">{formatDate(post.publishedAt)}</time>
            </div>
          </article>
        </PostWrapper>
      )
  }
}
