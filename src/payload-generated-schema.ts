/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:db-schema` to regenerate this file.
 */

import {
  pgTable,
  index,
  uniqueIndex,
  foreignKey,
  integer,
  varchar,
  boolean,
  text,
  jsonb,
  numeric,
  serial,
  timestamp,
  type AnyPgColumn,
  pgEnum,
} from '@payloadcms/db-postgres/drizzle/pg-core'
import { sql, relations } from '@payloadcms/db-postgres/drizzle'
export const enum_pages_hero_links_link_type = pgEnum('enum_pages_hero_links_link_type', [
  'reference',
  'custom',
])
export const enum_pages_hero_links_link_appearance = pgEnum(
  'enum_pages_hero_links_link_appearance',
  ['default', 'outline'],
)
export const enum_pages_blocks_cta_links_link_type = pgEnum(
  'enum_pages_blocks_cta_links_link_type',
  ['reference', 'custom'],
)
export const enum_pages_blocks_cta_links_link_appearance = pgEnum(
  'enum_pages_blocks_cta_links_link_appearance',
  ['default', 'outline'],
)
export const enum_pages_blocks_content_columns_size = pgEnum(
  'enum_pages_blocks_content_columns_size',
  ['oneThird', 'half', 'twoThirds', 'full'],
)
export const enum_pages_blocks_content_columns_link_type = pgEnum(
  'enum_pages_blocks_content_columns_link_type',
  ['reference', 'custom'],
)
export const enum_pages_blocks_content_columns_link_appearance = pgEnum(
  'enum_pages_blocks_content_columns_link_appearance',
  ['default', 'outline'],
)
export const enum_pages_blocks_archive_populate_by = pgEnum(
  'enum_pages_blocks_archive_populate_by',
  ['collection', 'selection'],
)
export const enum_pages_blocks_archive_relation_to = pgEnum(
  'enum_pages_blocks_archive_relation_to',
  ['posts'],
)
export const enum_pages_hero_type = pgEnum('enum_pages_hero_type', [
  'none',
  'highImpact',
  'mediumImpact',
  'lowImpact',
])
export const enum_pages_status = pgEnum('enum_pages_status', ['draft', 'published'])
export const enum__pages_v_version_hero_links_link_type = pgEnum(
  'enum__pages_v_version_hero_links_link_type',
  ['reference', 'custom'],
)
export const enum__pages_v_version_hero_links_link_appearance = pgEnum(
  'enum__pages_v_version_hero_links_link_appearance',
  ['default', 'outline'],
)
export const enum__pages_v_blocks_cta_links_link_type = pgEnum(
  'enum__pages_v_blocks_cta_links_link_type',
  ['reference', 'custom'],
)
export const enum__pages_v_blocks_cta_links_link_appearance = pgEnum(
  'enum__pages_v_blocks_cta_links_link_appearance',
  ['default', 'outline'],
)
export const enum__pages_v_blocks_content_columns_size = pgEnum(
  'enum__pages_v_blocks_content_columns_size',
  ['oneThird', 'half', 'twoThirds', 'full'],
)
export const enum__pages_v_blocks_content_columns_link_type = pgEnum(
  'enum__pages_v_blocks_content_columns_link_type',
  ['reference', 'custom'],
)
export const enum__pages_v_blocks_content_columns_link_appearance = pgEnum(
  'enum__pages_v_blocks_content_columns_link_appearance',
  ['default', 'outline'],
)
export const enum__pages_v_blocks_archive_populate_by = pgEnum(
  'enum__pages_v_blocks_archive_populate_by',
  ['collection', 'selection'],
)
export const enum__pages_v_blocks_archive_relation_to = pgEnum(
  'enum__pages_v_blocks_archive_relation_to',
  ['posts'],
)
export const enum__pages_v_version_hero_type = pgEnum('enum__pages_v_version_hero_type', [
  'none',
  'highImpact',
  'mediumImpact',
  'lowImpact',
])
export const enum__pages_v_version_status = pgEnum('enum__pages_v_version_status', [
  'draft',
  'published',
])
export const enum_posts_featured_image_type = pgEnum('enum_posts_featured_image_type', [
  'upload',
  'external',
])
export const enum_posts_status = pgEnum('enum_posts_status', ['draft', 'published'])
export const enum__posts_v_version_featured_image_type = pgEnum(
  'enum__posts_v_version_featured_image_type',
  ['upload', 'external'],
)
export const enum__posts_v_version_status = pgEnum('enum__posts_v_version_status', [
  'draft',
  'published',
])
export const enum_redirects_to_type = pgEnum('enum_redirects_to_type', ['reference', 'custom'])
export const enum_forms_confirmation_type = pgEnum('enum_forms_confirmation_type', [
  'message',
  'redirect',
])
export const enum_payload_jobs_log_task_slug = pgEnum('enum_payload_jobs_log_task_slug', [
  'inline',
  'schedulePublish',
])
export const enum_payload_jobs_log_state = pgEnum('enum_payload_jobs_log_state', [
  'failed',
  'succeeded',
])
export const enum_payload_jobs_task_slug = pgEnum('enum_payload_jobs_task_slug', [
  'inline',
  'schedulePublish',
])
export const enum_header_nav_items_link_type = pgEnum('enum_header_nav_items_link_type', [
  'reference',
  'custom',
])
export const enum_footer_nav_items_link_type = pgEnum('enum_footer_nav_items_link_type', [
  'reference',
  'custom',
])

export const pages_hero_links = pgTable(
  'pages_hero_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    link_type: enum_pages_hero_links_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance: enum_pages_hero_links_link_appearance('link_appearance').default('default'),
  },
  (columns) => ({
    _orderIdx: index('pages_hero_links_order_idx').on(columns._order),
    _parentIDIdx: index('pages_hero_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_hero_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_cta_links = pgTable(
  'pages_blocks_cta_links',
  {
    _order: integer('_order').notNull(),
    _parentID: varchar('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    link_type: enum_pages_blocks_cta_links_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum_pages_blocks_cta_links_link_appearance('link_appearance').default('default'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_cta_links_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_cta_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages_blocks_cta.id],
      name: 'pages_blocks_cta_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_cta = pgTable(
  'pages_blocks_cta',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    richText: jsonb('rich_text'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_cta_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_cta_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_cta_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_cta_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_content_columns = pgTable(
  'pages_blocks_content_columns',
  {
    _order: integer('_order').notNull(),
    _parentID: varchar('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    size: enum_pages_blocks_content_columns_size('size').default('oneThird'),
    richText: jsonb('rich_text'),
    enableLink: boolean('enable_link'),
    link_type: enum_pages_blocks_content_columns_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum_pages_blocks_content_columns_link_appearance('link_appearance').default('default'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_content_columns_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_content_columns_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages_blocks_content.id],
      name: 'pages_blocks_content_columns_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_content = pgTable(
  'pages_blocks_content',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_content_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_content_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_content_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_content_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_media_block = pgTable(
  'pages_blocks_media_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    media: integer('media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_media_block_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_media_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_media_block_path_idx').on(columns._path),
    pages_blocks_media_block_media_idx: index('pages_blocks_media_block_media_idx').on(
      columns.media,
    ),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_media_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_archive = pgTable(
  'pages_blocks_archive',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    introContent: jsonb('intro_content'),
    populateBy: enum_pages_blocks_archive_populate_by('populate_by').default('collection'),
    relationTo: enum_pages_blocks_archive_relation_to('relation_to').default('posts'),
    limit: numeric('limit').default('10'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_archive_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_archive_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_archive_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_archive_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_form_block = pgTable(
  'pages_blocks_form_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    form: integer('form_id').references(() => forms.id, {
      onDelete: 'set null',
    }),
    enableIntro: boolean('enable_intro'),
    introContent: jsonb('intro_content'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_form_block_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_form_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_form_block_path_idx').on(columns._path),
    pages_blocks_form_block_form_idx: index('pages_blocks_form_block_form_idx').on(columns.form),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_form_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages = pgTable(
  'pages',
  {
    id: serial('id').primaryKey(),
    title: varchar('title'),
    hero_type: enum_pages_hero_type('hero_type').default('lowImpact'),
    hero_richText: jsonb('hero_rich_text'),
    hero_media: integer('hero_media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_title: varchar('meta_title'),
    meta_image: integer('meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_description: varchar('meta_description'),
    publishedAt: timestamp('published_at', { mode: 'string', withTimezone: true, precision: 3 }),
    slug: varchar('slug'),
    slugLock: boolean('slug_lock').default(true),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    _status: enum_pages_status('_status').default('draft'),
  },
  (columns) => ({
    pages_hero_hero_media_idx: index('pages_hero_hero_media_idx').on(columns.hero_media),
    pages_meta_meta_image_idx: index('pages_meta_meta_image_idx').on(columns.meta_image),
    pages_slug_idx: index('pages_slug_idx').on(columns.slug),
    pages_updated_at_idx: index('pages_updated_at_idx').on(columns.updatedAt),
    pages_created_at_idx: index('pages_created_at_idx').on(columns.createdAt),
    pages__status_idx: index('pages__status_idx').on(columns._status),
  }),
)

export const pages_rels = pgTable(
  'pages_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    categoriesID: integer('categories_id'),
    postsID: integer('posts_id'),
  },
  (columns) => ({
    order: index('pages_rels_order_idx').on(columns.order),
    parentIdx: index('pages_rels_parent_idx').on(columns.parent),
    pathIdx: index('pages_rels_path_idx').on(columns.path),
    pages_rels_pages_id_idx: index('pages_rels_pages_id_idx').on(columns.pagesID),
    pages_rels_categories_id_idx: index('pages_rels_categories_id_idx').on(columns.categoriesID),
    pages_rels_posts_id_idx: index('pages_rels_posts_id_idx').on(columns.postsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [pages.id],
      name: 'pages_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'pages_rels_pages_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'pages_rels_categories_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'pages_rels_posts_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_version_hero_links = pgTable(
  '_pages_v_version_hero_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: serial('id').primaryKey(),
    link_type: enum__pages_v_version_hero_links_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum__pages_v_version_hero_links_link_appearance('link_appearance').default('default'),
    _uuid: varchar('_uuid'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_version_hero_links_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_version_hero_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_version_hero_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_cta_links = pgTable(
  '_pages_v_blocks_cta_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: serial('id').primaryKey(),
    link_type: enum__pages_v_blocks_cta_links_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum__pages_v_blocks_cta_links_link_appearance('link_appearance').default('default'),
    _uuid: varchar('_uuid'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_cta_links_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_cta_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v_blocks_cta.id],
      name: '_pages_v_blocks_cta_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_cta = pgTable(
  '_pages_v_blocks_cta',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    richText: jsonb('rich_text'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_cta_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_cta_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_cta_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_cta_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_content_columns = pgTable(
  '_pages_v_blocks_content_columns',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: serial('id').primaryKey(),
    size: enum__pages_v_blocks_content_columns_size('size').default('oneThird'),
    richText: jsonb('rich_text'),
    enableLink: boolean('enable_link'),
    link_type: enum__pages_v_blocks_content_columns_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum__pages_v_blocks_content_columns_link_appearance('link_appearance').default('default'),
    _uuid: varchar('_uuid'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_content_columns_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_content_columns_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v_blocks_content.id],
      name: '_pages_v_blocks_content_columns_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_content = pgTable(
  '_pages_v_blocks_content',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_content_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_content_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_content_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_content_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_media_block = pgTable(
  '_pages_v_blocks_media_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    media: integer('media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_media_block_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_media_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_media_block_path_idx').on(columns._path),
    _pages_v_blocks_media_block_media_idx: index('_pages_v_blocks_media_block_media_idx').on(
      columns.media,
    ),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_media_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_archive = pgTable(
  '_pages_v_blocks_archive',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    introContent: jsonb('intro_content'),
    populateBy: enum__pages_v_blocks_archive_populate_by('populate_by').default('collection'),
    relationTo: enum__pages_v_blocks_archive_relation_to('relation_to').default('posts'),
    limit: numeric('limit').default('10'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_archive_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_archive_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_archive_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_archive_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_form_block = pgTable(
  '_pages_v_blocks_form_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    form: integer('form_id').references(() => forms.id, {
      onDelete: 'set null',
    }),
    enableIntro: boolean('enable_intro'),
    introContent: jsonb('intro_content'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_form_block_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_form_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_form_block_path_idx').on(columns._path),
    _pages_v_blocks_form_block_form_idx: index('_pages_v_blocks_form_block_form_idx').on(
      columns.form,
    ),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_form_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v = pgTable(
  '_pages_v',
  {
    id: serial('id').primaryKey(),
    parent: integer('parent_id').references(() => pages.id, {
      onDelete: 'set null',
    }),
    version_title: varchar('version_title'),
    version_hero_type: enum__pages_v_version_hero_type('version_hero_type').default('lowImpact'),
    version_hero_richText: jsonb('version_hero_rich_text'),
    version_hero_media: integer('version_hero_media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    version_meta_title: varchar('version_meta_title'),
    version_meta_image: integer('version_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    version_meta_description: varchar('version_meta_description'),
    version_publishedAt: timestamp('version_published_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_slug: varchar('version_slug'),
    version_slugLock: boolean('version_slug_lock').default(true),
    version_updatedAt: timestamp('version_updated_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_createdAt: timestamp('version_created_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version__status: enum__pages_v_version_status('version__status').default('draft'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    latest: boolean('latest'),
    autosave: boolean('autosave'),
  },
  (columns) => ({
    _pages_v_parent_idx: index('_pages_v_parent_idx').on(columns.parent),
    _pages_v_version_hero_version_hero_media_idx: index(
      '_pages_v_version_hero_version_hero_media_idx',
    ).on(columns.version_hero_media),
    _pages_v_version_meta_version_meta_image_idx: index(
      '_pages_v_version_meta_version_meta_image_idx',
    ).on(columns.version_meta_image),
    _pages_v_version_version_slug_idx: index('_pages_v_version_version_slug_idx').on(
      columns.version_slug,
    ),
    _pages_v_version_version_updated_at_idx: index('_pages_v_version_version_updated_at_idx').on(
      columns.version_updatedAt,
    ),
    _pages_v_version_version_created_at_idx: index('_pages_v_version_version_created_at_idx').on(
      columns.version_createdAt,
    ),
    _pages_v_version_version__status_idx: index('_pages_v_version_version__status_idx').on(
      columns.version__status,
    ),
    _pages_v_created_at_idx: index('_pages_v_created_at_idx').on(columns.createdAt),
    _pages_v_updated_at_idx: index('_pages_v_updated_at_idx').on(columns.updatedAt),
    _pages_v_latest_idx: index('_pages_v_latest_idx').on(columns.latest),
    _pages_v_autosave_idx: index('_pages_v_autosave_idx').on(columns.autosave),
  }),
)

export const _pages_v_rels = pgTable(
  '_pages_v_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    categoriesID: integer('categories_id'),
    postsID: integer('posts_id'),
  },
  (columns) => ({
    order: index('_pages_v_rels_order_idx').on(columns.order),
    parentIdx: index('_pages_v_rels_parent_idx').on(columns.parent),
    pathIdx: index('_pages_v_rels_path_idx').on(columns.path),
    _pages_v_rels_pages_id_idx: index('_pages_v_rels_pages_id_idx').on(columns.pagesID),
    _pages_v_rels_categories_id_idx: index('_pages_v_rels_categories_id_idx').on(
      columns.categoriesID,
    ),
    _pages_v_rels_posts_id_idx: index('_pages_v_rels_posts_id_idx').on(columns.postsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: '_pages_v_rels_pages_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: '_pages_v_rels_categories_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: '_pages_v_rels_posts_fk',
    }).onDelete('cascade'),
  }),
)

export const posts_populated_authors = pgTable(
  'posts_populated_authors',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name'),
  },
  (columns) => ({
    _orderIdx: index('posts_populated_authors_order_idx').on(columns._order),
    _parentIDIdx: index('posts_populated_authors_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [posts.id],
      name: 'posts_populated_authors_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const posts = pgTable(
  'posts',
  {
    id: serial('id').primaryKey(),
    title: varchar('title'),
    featuredImage_type: enum_posts_featured_image_type('featured_image_type').default('upload'),
    featuredImage_media: integer('featured_image_media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    featuredImage_url: varchar('featured_image_url'),
    featuredImage_alt: varchar('featured_image_alt'),
    content: jsonb('content'),
    summary: varchar('summary'),
    source_url: varchar('source_url'),
    source_site_name: varchar('source_site_name'),
    extra: jsonb('extra').default(sql`'{}'::jsonb`),
    meta_title: varchar('meta_title'),
    meta_image: integer('meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_description: varchar('meta_description'),
    publishedAt: timestamp('published_at', { mode: 'string', withTimezone: true, precision: 3 }),
    slug: varchar('slug'),
    slugLock: boolean('slug_lock').default(true),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    _status: enum_posts_status('_status').default('draft'),
  },
  (columns) => ({
    posts_featured_image_featured_image_media_idx: index(
      'posts_featured_image_featured_image_media_idx',
    ).on(columns.featuredImage_media),
    posts_meta_meta_image_idx: index('posts_meta_meta_image_idx').on(columns.meta_image),
    posts_slug_idx: index('posts_slug_idx').on(columns.slug),
    posts_updated_at_idx: index('posts_updated_at_idx').on(columns.updatedAt),
    posts_created_at_idx: index('posts_created_at_idx').on(columns.createdAt),
    posts__status_idx: index('posts__status_idx').on(columns._status),
  }),
)

export const posts_rels = pgTable(
  'posts_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    postsID: integer('posts_id'),
    categoriesID: integer('categories_id'),
    usersID: integer('users_id'),
  },
  (columns) => ({
    order: index('posts_rels_order_idx').on(columns.order),
    parentIdx: index('posts_rels_parent_idx').on(columns.parent),
    pathIdx: index('posts_rels_path_idx').on(columns.path),
    posts_rels_posts_id_idx: index('posts_rels_posts_id_idx').on(columns.postsID),
    posts_rels_categories_id_idx: index('posts_rels_categories_id_idx').on(columns.categoriesID),
    posts_rels_users_id_idx: index('posts_rels_users_id_idx').on(columns.usersID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [posts.id],
      name: 'posts_rels_parent_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'posts_rels_posts_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'posts_rels_categories_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'posts_rels_users_fk',
    }).onDelete('cascade'),
  }),
)

export const _posts_v_version_populated_authors = pgTable(
  '_posts_v_version_populated_authors',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: serial('id').primaryKey(),
    _uuid: varchar('_uuid'),
    name: varchar('name'),
  },
  (columns) => ({
    _orderIdx: index('_posts_v_version_populated_authors_order_idx').on(columns._order),
    _parentIDIdx: index('_posts_v_version_populated_authors_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_posts_v.id],
      name: '_posts_v_version_populated_authors_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _posts_v = pgTable(
  '_posts_v',
  {
    id: serial('id').primaryKey(),
    parent: integer('parent_id').references(() => posts.id, {
      onDelete: 'set null',
    }),
    version_title: varchar('version_title'),
    version_featuredImage_type: enum__posts_v_version_featured_image_type(
      'version_featured_image_type',
    ).default('upload'),
    version_featuredImage_media: integer('version_featured_image_media_id').references(
      () => media.id,
      {
        onDelete: 'set null',
      },
    ),
    version_featuredImage_url: varchar('version_featured_image_url'),
    version_featuredImage_alt: varchar('version_featured_image_alt'),
    version_content: jsonb('version_content'),
    version_summary: varchar('version_summary'),
    version_source_url: varchar('version_source_url'),
    version_source_site_name: varchar('version_source_site_name'),
    version_extra: jsonb('version_extra').default(sql`'{}'::jsonb`),
    version_meta_title: varchar('version_meta_title'),
    version_meta_image: integer('version_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    version_meta_description: varchar('version_meta_description'),
    version_publishedAt: timestamp('version_published_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_slug: varchar('version_slug'),
    version_slugLock: boolean('version_slug_lock').default(true),
    version_updatedAt: timestamp('version_updated_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_createdAt: timestamp('version_created_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version__status: enum__posts_v_version_status('version__status').default('draft'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    latest: boolean('latest'),
    autosave: boolean('autosave'),
  },
  (columns) => ({
    _posts_v_parent_idx: index('_posts_v_parent_idx').on(columns.parent),
    _posts_v_version_featured_image_version_featured_image_media_idx: index(
      '_posts_v_version_featured_image_version_featured_image_media_idx',
    ).on(columns.version_featuredImage_media),
    _posts_v_version_meta_version_meta_image_idx: index(
      '_posts_v_version_meta_version_meta_image_idx',
    ).on(columns.version_meta_image),
    _posts_v_version_version_slug_idx: index('_posts_v_version_version_slug_idx').on(
      columns.version_slug,
    ),
    _posts_v_version_version_updated_at_idx: index('_posts_v_version_version_updated_at_idx').on(
      columns.version_updatedAt,
    ),
    _posts_v_version_version_created_at_idx: index('_posts_v_version_version_created_at_idx').on(
      columns.version_createdAt,
    ),
    _posts_v_version_version__status_idx: index('_posts_v_version_version__status_idx').on(
      columns.version__status,
    ),
    _posts_v_created_at_idx: index('_posts_v_created_at_idx').on(columns.createdAt),
    _posts_v_updated_at_idx: index('_posts_v_updated_at_idx').on(columns.updatedAt),
    _posts_v_latest_idx: index('_posts_v_latest_idx').on(columns.latest),
    _posts_v_autosave_idx: index('_posts_v_autosave_idx').on(columns.autosave),
  }),
)

export const _posts_v_rels = pgTable(
  '_posts_v_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    postsID: integer('posts_id'),
    categoriesID: integer('categories_id'),
    usersID: integer('users_id'),
  },
  (columns) => ({
    order: index('_posts_v_rels_order_idx').on(columns.order),
    parentIdx: index('_posts_v_rels_parent_idx').on(columns.parent),
    pathIdx: index('_posts_v_rels_path_idx').on(columns.path),
    _posts_v_rels_posts_id_idx: index('_posts_v_rels_posts_id_idx').on(columns.postsID),
    _posts_v_rels_categories_id_idx: index('_posts_v_rels_categories_id_idx').on(
      columns.categoriesID,
    ),
    _posts_v_rels_users_id_idx: index('_posts_v_rels_users_id_idx').on(columns.usersID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [_posts_v.id],
      name: '_posts_v_rels_parent_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: '_posts_v_rels_posts_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: '_posts_v_rels_categories_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: '_posts_v_rels_users_fk',
    }).onDelete('cascade'),
  }),
)

export const media = pgTable(
  'media',
  {
    id: serial('id').primaryKey(),
    alt: varchar('alt'),
    caption: jsonb('caption'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    url: varchar('url'),
    thumbnailURL: varchar('thumbnail_u_r_l'),
    filename: varchar('filename'),
    mimeType: varchar('mime_type'),
    filesize: numeric('filesize'),
    width: numeric('width'),
    height: numeric('height'),
    focalX: numeric('focal_x'),
    focalY: numeric('focal_y'),
    sizes_thumbnail_url: varchar('sizes_thumbnail_url'),
    sizes_thumbnail_width: numeric('sizes_thumbnail_width'),
    sizes_thumbnail_height: numeric('sizes_thumbnail_height'),
    sizes_thumbnail_mimeType: varchar('sizes_thumbnail_mime_type'),
    sizes_thumbnail_filesize: numeric('sizes_thumbnail_filesize'),
    sizes_thumbnail_filename: varchar('sizes_thumbnail_filename'),
    sizes_square_url: varchar('sizes_square_url'),
    sizes_square_width: numeric('sizes_square_width'),
    sizes_square_height: numeric('sizes_square_height'),
    sizes_square_mimeType: varchar('sizes_square_mime_type'),
    sizes_square_filesize: numeric('sizes_square_filesize'),
    sizes_square_filename: varchar('sizes_square_filename'),
    sizes_small_url: varchar('sizes_small_url'),
    sizes_small_width: numeric('sizes_small_width'),
    sizes_small_height: numeric('sizes_small_height'),
    sizes_small_mimeType: varchar('sizes_small_mime_type'),
    sizes_small_filesize: numeric('sizes_small_filesize'),
    sizes_small_filename: varchar('sizes_small_filename'),
    sizes_medium_url: varchar('sizes_medium_url'),
    sizes_medium_width: numeric('sizes_medium_width'),
    sizes_medium_height: numeric('sizes_medium_height'),
    sizes_medium_mimeType: varchar('sizes_medium_mime_type'),
    sizes_medium_filesize: numeric('sizes_medium_filesize'),
    sizes_medium_filename: varchar('sizes_medium_filename'),
    sizes_large_url: varchar('sizes_large_url'),
    sizes_large_width: numeric('sizes_large_width'),
    sizes_large_height: numeric('sizes_large_height'),
    sizes_large_mimeType: varchar('sizes_large_mime_type'),
    sizes_large_filesize: numeric('sizes_large_filesize'),
    sizes_large_filename: varchar('sizes_large_filename'),
    sizes_xlarge_url: varchar('sizes_xlarge_url'),
    sizes_xlarge_width: numeric('sizes_xlarge_width'),
    sizes_xlarge_height: numeric('sizes_xlarge_height'),
    sizes_xlarge_mimeType: varchar('sizes_xlarge_mime_type'),
    sizes_xlarge_filesize: numeric('sizes_xlarge_filesize'),
    sizes_xlarge_filename: varchar('sizes_xlarge_filename'),
    sizes_og_url: varchar('sizes_og_url'),
    sizes_og_width: numeric('sizes_og_width'),
    sizes_og_height: numeric('sizes_og_height'),
    sizes_og_mimeType: varchar('sizes_og_mime_type'),
    sizes_og_filesize: numeric('sizes_og_filesize'),
    sizes_og_filename: varchar('sizes_og_filename'),
  },
  (columns) => ({
    media_updated_at_idx: index('media_updated_at_idx').on(columns.updatedAt),
    media_created_at_idx: index('media_created_at_idx').on(columns.createdAt),
    media_filename_idx: uniqueIndex('media_filename_idx').on(columns.filename),
    media_sizes_thumbnail_sizes_thumbnail_filename_idx: index(
      'media_sizes_thumbnail_sizes_thumbnail_filename_idx',
    ).on(columns.sizes_thumbnail_filename),
    media_sizes_square_sizes_square_filename_idx: index(
      'media_sizes_square_sizes_square_filename_idx',
    ).on(columns.sizes_square_filename),
    media_sizes_small_sizes_small_filename_idx: index(
      'media_sizes_small_sizes_small_filename_idx',
    ).on(columns.sizes_small_filename),
    media_sizes_medium_sizes_medium_filename_idx: index(
      'media_sizes_medium_sizes_medium_filename_idx',
    ).on(columns.sizes_medium_filename),
    media_sizes_large_sizes_large_filename_idx: index(
      'media_sizes_large_sizes_large_filename_idx',
    ).on(columns.sizes_large_filename),
    media_sizes_xlarge_sizes_xlarge_filename_idx: index(
      'media_sizes_xlarge_sizes_xlarge_filename_idx',
    ).on(columns.sizes_xlarge_filename),
    media_sizes_og_sizes_og_filename_idx: index('media_sizes_og_sizes_og_filename_idx').on(
      columns.sizes_og_filename,
    ),
  }),
)

export const categories_breadcrumbs = pgTable(
  'categories_breadcrumbs',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    doc: integer('doc_id').references(() => categories.id, {
      onDelete: 'set null',
    }),
    url: varchar('url'),
    label: varchar('label'),
  },
  (columns) => ({
    _orderIdx: index('categories_breadcrumbs_order_idx').on(columns._order),
    _parentIDIdx: index('categories_breadcrumbs_parent_id_idx').on(columns._parentID),
    categories_breadcrumbs_doc_idx: index('categories_breadcrumbs_doc_idx').on(columns.doc),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [categories.id],
      name: 'categories_breadcrumbs_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const categories = pgTable(
  'categories',
  {
    id: serial('id').primaryKey(),
    title: varchar('title').notNull(),
    slug: varchar('slug'),
    slugLock: boolean('slug_lock').default(true),
    parent: integer('parent_id').references((): AnyPgColumn => categories.id, {
      onDelete: 'set null',
    }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    categories_slug_idx: index('categories_slug_idx').on(columns.slug),
    categories_parent_idx: index('categories_parent_idx').on(columns.parent),
    categories_updated_at_idx: index('categories_updated_at_idx').on(columns.updatedAt),
    categories_created_at_idx: index('categories_created_at_idx').on(columns.createdAt),
  }),
)

export const users = pgTable(
  'users',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    email: varchar('email').notNull(),
    resetPasswordToken: varchar('reset_password_token'),
    resetPasswordExpiration: timestamp('reset_password_expiration', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    salt: varchar('salt'),
    hash: varchar('hash'),
    loginAttempts: numeric('login_attempts').default('0'),
    lockUntil: timestamp('lock_until', { mode: 'string', withTimezone: true, precision: 3 }),
  },
  (columns) => ({
    users_updated_at_idx: index('users_updated_at_idx').on(columns.updatedAt),
    users_created_at_idx: index('users_created_at_idx').on(columns.createdAt),
    users_email_idx: uniqueIndex('users_email_idx').on(columns.email),
  }),
)

export const redirects = pgTable(
  'redirects',
  {
    id: serial('id').primaryKey(),
    from: varchar('from').notNull(),
    to_type: enum_redirects_to_type('to_type').default('reference'),
    to_url: varchar('to_url'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    redirects_from_idx: index('redirects_from_idx').on(columns.from),
    redirects_updated_at_idx: index('redirects_updated_at_idx').on(columns.updatedAt),
    redirects_created_at_idx: index('redirects_created_at_idx').on(columns.createdAt),
  }),
)

export const redirects_rels = pgTable(
  'redirects_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    postsID: integer('posts_id'),
  },
  (columns) => ({
    order: index('redirects_rels_order_idx').on(columns.order),
    parentIdx: index('redirects_rels_parent_idx').on(columns.parent),
    pathIdx: index('redirects_rels_path_idx').on(columns.path),
    redirects_rels_pages_id_idx: index('redirects_rels_pages_id_idx').on(columns.pagesID),
    redirects_rels_posts_id_idx: index('redirects_rels_posts_id_idx').on(columns.postsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [redirects.id],
      name: 'redirects_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'redirects_rels_pages_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'redirects_rels_posts_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_checkbox = pgTable(
  'forms_blocks_checkbox',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    required: boolean('required'),
    defaultValue: boolean('default_value'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_checkbox_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_checkbox_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_checkbox_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_checkbox_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_country = pgTable(
  'forms_blocks_country',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_country_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_country_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_country_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_country_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_email = pgTable(
  'forms_blocks_email',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_email_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_email_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_email_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_email_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_message = pgTable(
  'forms_blocks_message',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    message: jsonb('message'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_message_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_message_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_message_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_message_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_number = pgTable(
  'forms_blocks_number',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    defaultValue: numeric('default_value'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_number_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_number_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_number_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_number_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_select_options = pgTable(
  'forms_blocks_select_options',
  {
    _order: integer('_order').notNull(),
    _parentID: varchar('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    label: varchar('label').notNull(),
    value: varchar('value').notNull(),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_select_options_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_select_options_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms_blocks_select.id],
      name: 'forms_blocks_select_options_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_select = pgTable(
  'forms_blocks_select',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    defaultValue: varchar('default_value'),
    placeholder: varchar('placeholder'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_select_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_select_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_select_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_select_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_state = pgTable(
  'forms_blocks_state',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_state_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_state_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_state_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_state_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_text = pgTable(
  'forms_blocks_text',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    defaultValue: varchar('default_value'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_text_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_text_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_text_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_text_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_blocks_textarea = pgTable(
  'forms_blocks_textarea',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    label: varchar('label'),
    width: numeric('width'),
    defaultValue: varchar('default_value'),
    required: boolean('required'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('forms_blocks_textarea_order_idx').on(columns._order),
    _parentIDIdx: index('forms_blocks_textarea_parent_id_idx').on(columns._parentID),
    _pathIdx: index('forms_blocks_textarea_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_blocks_textarea_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms_emails = pgTable(
  'forms_emails',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    emailTo: varchar('email_to'),
    cc: varchar('cc'),
    bcc: varchar('bcc'),
    replyTo: varchar('reply_to'),
    emailFrom: varchar('email_from'),
    subject: varchar('subject').notNull().default("You''ve received a new message."),
    message: jsonb('message'),
  },
  (columns) => ({
    _orderIdx: index('forms_emails_order_idx').on(columns._order),
    _parentIDIdx: index('forms_emails_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [forms.id],
      name: 'forms_emails_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const forms = pgTable(
  'forms',
  {
    id: serial('id').primaryKey(),
    title: varchar('title').notNull(),
    submitButtonLabel: varchar('submit_button_label'),
    confirmationType: enum_forms_confirmation_type('confirmation_type').default('message'),
    confirmationMessage: jsonb('confirmation_message'),
    redirect_url: varchar('redirect_url'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    forms_updated_at_idx: index('forms_updated_at_idx').on(columns.updatedAt),
    forms_created_at_idx: index('forms_created_at_idx').on(columns.createdAt),
  }),
)

export const form_submissions_submission_data = pgTable(
  'form_submissions_submission_data',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    field: varchar('field').notNull(),
    value: varchar('value').notNull(),
  },
  (columns) => ({
    _orderIdx: index('form_submissions_submission_data_order_idx').on(columns._order),
    _parentIDIdx: index('form_submissions_submission_data_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [form_submissions.id],
      name: 'form_submissions_submission_data_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const form_submissions = pgTable(
  'form_submissions',
  {
    id: serial('id').primaryKey(),
    form: integer('form_id')
      .notNull()
      .references(() => forms.id, {
        onDelete: 'set null',
      }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    form_submissions_form_idx: index('form_submissions_form_idx').on(columns.form),
    form_submissions_updated_at_idx: index('form_submissions_updated_at_idx').on(columns.updatedAt),
    form_submissions_created_at_idx: index('form_submissions_created_at_idx').on(columns.createdAt),
  }),
)

export const search_categories = pgTable(
  'search_categories',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    relationTo: varchar('relation_to'),
    title: varchar('title'),
  },
  (columns) => ({
    _orderIdx: index('search_categories_order_idx').on(columns._order),
    _parentIDIdx: index('search_categories_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [search.id],
      name: 'search_categories_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const search = pgTable(
  'search',
  {
    id: serial('id').primaryKey(),
    title: varchar('title'),
    priority: numeric('priority'),
    slug: varchar('slug'),
    meta_title: varchar('meta_title'),
    meta_description: varchar('meta_description'),
    meta_image: integer('meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    search_slug_idx: index('search_slug_idx').on(columns.slug),
    search_meta_meta_image_idx: index('search_meta_meta_image_idx').on(columns.meta_image),
    search_updated_at_idx: index('search_updated_at_idx').on(columns.updatedAt),
    search_created_at_idx: index('search_created_at_idx').on(columns.createdAt),
  }),
)

export const search_rels = pgTable(
  'search_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    postsID: integer('posts_id'),
  },
  (columns) => ({
    order: index('search_rels_order_idx').on(columns.order),
    parentIdx: index('search_rels_parent_idx').on(columns.parent),
    pathIdx: index('search_rels_path_idx').on(columns.path),
    search_rels_posts_id_idx: index('search_rels_posts_id_idx').on(columns.postsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [search.id],
      name: 'search_rels_parent_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'search_rels_posts_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_jobs_log = pgTable(
  'payload_jobs_log',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    executedAt: timestamp('executed_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    completedAt: timestamp('completed_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    taskSlug: enum_payload_jobs_log_task_slug('task_slug').notNull(),
    taskID: varchar('task_i_d').notNull(),
    input: jsonb('input'),
    output: jsonb('output'),
    state: enum_payload_jobs_log_state('state').notNull(),
    error: jsonb('error'),
  },
  (columns) => ({
    _orderIdx: index('payload_jobs_log_order_idx').on(columns._order),
    _parentIDIdx: index('payload_jobs_log_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [payload_jobs.id],
      name: 'payload_jobs_log_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_jobs = pgTable(
  'payload_jobs',
  {
    id: serial('id').primaryKey(),
    input: jsonb('input'),
    completedAt: timestamp('completed_at', { mode: 'string', withTimezone: true, precision: 3 }),
    totalTried: numeric('total_tried').default('0'),
    hasError: boolean('has_error').default(false),
    error: jsonb('error'),
    taskSlug: enum_payload_jobs_task_slug('task_slug'),
    queue: varchar('queue').default('default'),
    waitUntil: timestamp('wait_until', { mode: 'string', withTimezone: true, precision: 3 }),
    processing: boolean('processing').default(false),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_jobs_completed_at_idx: index('payload_jobs_completed_at_idx').on(columns.completedAt),
    payload_jobs_total_tried_idx: index('payload_jobs_total_tried_idx').on(columns.totalTried),
    payload_jobs_has_error_idx: index('payload_jobs_has_error_idx').on(columns.hasError),
    payload_jobs_task_slug_idx: index('payload_jobs_task_slug_idx').on(columns.taskSlug),
    payload_jobs_queue_idx: index('payload_jobs_queue_idx').on(columns.queue),
    payload_jobs_wait_until_idx: index('payload_jobs_wait_until_idx').on(columns.waitUntil),
    payload_jobs_processing_idx: index('payload_jobs_processing_idx').on(columns.processing),
    payload_jobs_updated_at_idx: index('payload_jobs_updated_at_idx').on(columns.updatedAt),
    payload_jobs_created_at_idx: index('payload_jobs_created_at_idx').on(columns.createdAt),
  }),
)

export const payload_locked_documents = pgTable(
  'payload_locked_documents',
  {
    id: serial('id').primaryKey(),
    globalSlug: varchar('global_slug'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_locked_documents_global_slug_idx: index('payload_locked_documents_global_slug_idx').on(
      columns.globalSlug,
    ),
    payload_locked_documents_updated_at_idx: index('payload_locked_documents_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_locked_documents_created_at_idx: index('payload_locked_documents_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const payload_locked_documents_rels = pgTable(
  'payload_locked_documents_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    postsID: integer('posts_id'),
    mediaID: integer('media_id'),
    categoriesID: integer('categories_id'),
    usersID: integer('users_id'),
    redirectsID: integer('redirects_id'),
    formsID: integer('forms_id'),
    'form-submissionsID': integer('form_submissions_id'),
    searchID: integer('search_id'),
    'payload-jobsID': integer('payload_jobs_id'),
  },
  (columns) => ({
    order: index('payload_locked_documents_rels_order_idx').on(columns.order),
    parentIdx: index('payload_locked_documents_rels_parent_idx').on(columns.parent),
    pathIdx: index('payload_locked_documents_rels_path_idx').on(columns.path),
    payload_locked_documents_rels_pages_id_idx: index(
      'payload_locked_documents_rels_pages_id_idx',
    ).on(columns.pagesID),
    payload_locked_documents_rels_posts_id_idx: index(
      'payload_locked_documents_rels_posts_id_idx',
    ).on(columns.postsID),
    payload_locked_documents_rels_media_id_idx: index(
      'payload_locked_documents_rels_media_id_idx',
    ).on(columns.mediaID),
    payload_locked_documents_rels_categories_id_idx: index(
      'payload_locked_documents_rels_categories_id_idx',
    ).on(columns.categoriesID),
    payload_locked_documents_rels_users_id_idx: index(
      'payload_locked_documents_rels_users_id_idx',
    ).on(columns.usersID),
    payload_locked_documents_rels_redirects_id_idx: index(
      'payload_locked_documents_rels_redirects_id_idx',
    ).on(columns.redirectsID),
    payload_locked_documents_rels_forms_id_idx: index(
      'payload_locked_documents_rels_forms_id_idx',
    ).on(columns.formsID),
    payload_locked_documents_rels_form_submissions_id_idx: index(
      'payload_locked_documents_rels_form_submissions_id_idx',
    ).on(columns['form-submissionsID']),
    payload_locked_documents_rels_search_id_idx: index(
      'payload_locked_documents_rels_search_id_idx',
    ).on(columns.searchID),
    payload_locked_documents_rels_payload_jobs_id_idx: index(
      'payload_locked_documents_rels_payload_jobs_id_idx',
    ).on(columns['payload-jobsID']),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [payload_locked_documents.id],
      name: 'payload_locked_documents_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'payload_locked_documents_rels_pages_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'payload_locked_documents_rels_posts_fk',
    }).onDelete('cascade'),
    mediaIdFk: foreignKey({
      columns: [columns['mediaID']],
      foreignColumns: [media.id],
      name: 'payload_locked_documents_rels_media_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'payload_locked_documents_rels_categories_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'payload_locked_documents_rels_users_fk',
    }).onDelete('cascade'),
    redirectsIdFk: foreignKey({
      columns: [columns['redirectsID']],
      foreignColumns: [redirects.id],
      name: 'payload_locked_documents_rels_redirects_fk',
    }).onDelete('cascade'),
    formsIdFk: foreignKey({
      columns: [columns['formsID']],
      foreignColumns: [forms.id],
      name: 'payload_locked_documents_rels_forms_fk',
    }).onDelete('cascade'),
    'form-submissionsIdFk': foreignKey({
      columns: [columns['form-submissionsID']],
      foreignColumns: [form_submissions.id],
      name: 'payload_locked_documents_rels_form_submissions_fk',
    }).onDelete('cascade'),
    searchIdFk: foreignKey({
      columns: [columns['searchID']],
      foreignColumns: [search.id],
      name: 'payload_locked_documents_rels_search_fk',
    }).onDelete('cascade'),
    'payload-jobsIdFk': foreignKey({
      columns: [columns['payload-jobsID']],
      foreignColumns: [payload_jobs.id],
      name: 'payload_locked_documents_rels_payload_jobs_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_preferences = pgTable(
  'payload_preferences',
  {
    id: serial('id').primaryKey(),
    key: varchar('key'),
    value: jsonb('value'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_preferences_key_idx: index('payload_preferences_key_idx').on(columns.key),
    payload_preferences_updated_at_idx: index('payload_preferences_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_preferences_created_at_idx: index('payload_preferences_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const payload_preferences_rels = pgTable(
  'payload_preferences_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    usersID: integer('users_id'),
  },
  (columns) => ({
    order: index('payload_preferences_rels_order_idx').on(columns.order),
    parentIdx: index('payload_preferences_rels_parent_idx').on(columns.parent),
    pathIdx: index('payload_preferences_rels_path_idx').on(columns.path),
    payload_preferences_rels_users_id_idx: index('payload_preferences_rels_users_id_idx').on(
      columns.usersID,
    ),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [payload_preferences.id],
      name: 'payload_preferences_rels_parent_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'payload_preferences_rels_users_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_migrations = pgTable(
  'payload_migrations',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    batch: numeric('batch'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_migrations_updated_at_idx: index('payload_migrations_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_migrations_created_at_idx: index('payload_migrations_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const header_nav_items = pgTable(
  'header_nav_items',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    link_type: enum_header_nav_items_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label').notNull(),
  },
  (columns) => ({
    _orderIdx: index('header_nav_items_order_idx').on(columns._order),
    _parentIDIdx: index('header_nav_items_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [header.id],
      name: 'header_nav_items_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const header = pgTable('header', {
  id: serial('id').primaryKey(),
  updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 }),
  createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 }),
})

export const header_rels = pgTable(
  'header_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    categoriesID: integer('categories_id'),
    postsID: integer('posts_id'),
  },
  (columns) => ({
    order: index('header_rels_order_idx').on(columns.order),
    parentIdx: index('header_rels_parent_idx').on(columns.parent),
    pathIdx: index('header_rels_path_idx').on(columns.path),
    header_rels_pages_id_idx: index('header_rels_pages_id_idx').on(columns.pagesID),
    header_rels_categories_id_idx: index('header_rels_categories_id_idx').on(columns.categoriesID),
    header_rels_posts_id_idx: index('header_rels_posts_id_idx').on(columns.postsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [header.id],
      name: 'header_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'header_rels_pages_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'header_rels_categories_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'header_rels_posts_fk',
    }).onDelete('cascade'),
  }),
)

export const footer_nav_items = pgTable(
  'footer_nav_items',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    link_type: enum_footer_nav_items_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label').notNull(),
  },
  (columns) => ({
    _orderIdx: index('footer_nav_items_order_idx').on(columns._order),
    _parentIDIdx: index('footer_nav_items_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [footer.id],
      name: 'footer_nav_items_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const footer = pgTable('footer', {
  id: serial('id').primaryKey(),
  updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 }),
  createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 }),
})

export const footer_rels = pgTable(
  'footer_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    categoriesID: integer('categories_id'),
    postsID: integer('posts_id'),
  },
  (columns) => ({
    order: index('footer_rels_order_idx').on(columns.order),
    parentIdx: index('footer_rels_parent_idx').on(columns.parent),
    pathIdx: index('footer_rels_path_idx').on(columns.path),
    footer_rels_pages_id_idx: index('footer_rels_pages_id_idx').on(columns.pagesID),
    footer_rels_categories_id_idx: index('footer_rels_categories_id_idx').on(columns.categoriesID),
    footer_rels_posts_id_idx: index('footer_rels_posts_id_idx').on(columns.postsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [footer.id],
      name: 'footer_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'footer_rels_pages_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'footer_rels_categories_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'footer_rels_posts_fk',
    }).onDelete('cascade'),
  }),
)

export const home_sections = pgTable(
  'home_sections',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    title: varchar('title').notNull(),
    category: integer('category_id')
      .notNull()
      .references(() => categories.id, {
        onDelete: 'set null',
      }),
  },
  (columns) => ({
    _orderIdx: index('home_sections_order_idx').on(columns._order),
    _parentIDIdx: index('home_sections_parent_id_idx').on(columns._parentID),
    home_sections_category_idx: index('home_sections_category_idx').on(columns.category),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [home.id],
      name: 'home_sections_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const home = pgTable('home', {
  id: serial('id').primaryKey(),
  updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 }),
  createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 }),
})

export const relations_pages_hero_links = relations(pages_hero_links, ({ one }) => ({
  _parentID: one(pages, {
    fields: [pages_hero_links._parentID],
    references: [pages.id],
    relationName: 'hero_links',
  }),
}))
export const relations_pages_blocks_cta_links = relations(pages_blocks_cta_links, ({ one }) => ({
  _parentID: one(pages_blocks_cta, {
    fields: [pages_blocks_cta_links._parentID],
    references: [pages_blocks_cta.id],
    relationName: 'links',
  }),
}))
export const relations_pages_blocks_cta = relations(pages_blocks_cta, ({ one, many }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_cta._parentID],
    references: [pages.id],
    relationName: '_blocks_cta',
  }),
  links: many(pages_blocks_cta_links, {
    relationName: 'links',
  }),
}))
export const relations_pages_blocks_content_columns = relations(
  pages_blocks_content_columns,
  ({ one }) => ({
    _parentID: one(pages_blocks_content, {
      fields: [pages_blocks_content_columns._parentID],
      references: [pages_blocks_content.id],
      relationName: 'columns',
    }),
  }),
)
export const relations_pages_blocks_content = relations(pages_blocks_content, ({ one, many }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_content._parentID],
    references: [pages.id],
    relationName: '_blocks_content',
  }),
  columns: many(pages_blocks_content_columns, {
    relationName: 'columns',
  }),
}))
export const relations_pages_blocks_media_block = relations(
  pages_blocks_media_block,
  ({ one }) => ({
    _parentID: one(pages, {
      fields: [pages_blocks_media_block._parentID],
      references: [pages.id],
      relationName: '_blocks_mediaBlock',
    }),
    media: one(media, {
      fields: [pages_blocks_media_block.media],
      references: [media.id],
      relationName: 'media',
    }),
  }),
)
export const relations_pages_blocks_archive = relations(pages_blocks_archive, ({ one }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_archive._parentID],
    references: [pages.id],
    relationName: '_blocks_archive',
  }),
}))
export const relations_pages_blocks_form_block = relations(pages_blocks_form_block, ({ one }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_form_block._parentID],
    references: [pages.id],
    relationName: '_blocks_formBlock',
  }),
  form: one(forms, {
    fields: [pages_blocks_form_block.form],
    references: [forms.id],
    relationName: 'form',
  }),
}))
export const relations_pages_rels = relations(pages_rels, ({ one }) => ({
  parent: one(pages, {
    fields: [pages_rels.parent],
    references: [pages.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [pages_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  categoriesID: one(categories, {
    fields: [pages_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  postsID: one(posts, {
    fields: [pages_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
}))
export const relations_pages = relations(pages, ({ one, many }) => ({
  hero_links: many(pages_hero_links, {
    relationName: 'hero_links',
  }),
  hero_media: one(media, {
    fields: [pages.hero_media],
    references: [media.id],
    relationName: 'hero_media',
  }),
  _blocks_cta: many(pages_blocks_cta, {
    relationName: '_blocks_cta',
  }),
  _blocks_content: many(pages_blocks_content, {
    relationName: '_blocks_content',
  }),
  _blocks_mediaBlock: many(pages_blocks_media_block, {
    relationName: '_blocks_mediaBlock',
  }),
  _blocks_archive: many(pages_blocks_archive, {
    relationName: '_blocks_archive',
  }),
  _blocks_formBlock: many(pages_blocks_form_block, {
    relationName: '_blocks_formBlock',
  }),
  meta_image: one(media, {
    fields: [pages.meta_image],
    references: [media.id],
    relationName: 'meta_image',
  }),
  _rels: many(pages_rels, {
    relationName: '_rels',
  }),
}))
export const relations__pages_v_version_hero_links = relations(
  _pages_v_version_hero_links,
  ({ one }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_version_hero_links._parentID],
      references: [_pages_v.id],
      relationName: 'version_hero_links',
    }),
  }),
)
export const relations__pages_v_blocks_cta_links = relations(
  _pages_v_blocks_cta_links,
  ({ one }) => ({
    _parentID: one(_pages_v_blocks_cta, {
      fields: [_pages_v_blocks_cta_links._parentID],
      references: [_pages_v_blocks_cta.id],
      relationName: 'links',
    }),
  }),
)
export const relations__pages_v_blocks_cta = relations(_pages_v_blocks_cta, ({ one, many }) => ({
  _parentID: one(_pages_v, {
    fields: [_pages_v_blocks_cta._parentID],
    references: [_pages_v.id],
    relationName: '_blocks_cta',
  }),
  links: many(_pages_v_blocks_cta_links, {
    relationName: 'links',
  }),
}))
export const relations__pages_v_blocks_content_columns = relations(
  _pages_v_blocks_content_columns,
  ({ one }) => ({
    _parentID: one(_pages_v_blocks_content, {
      fields: [_pages_v_blocks_content_columns._parentID],
      references: [_pages_v_blocks_content.id],
      relationName: 'columns',
    }),
  }),
)
export const relations__pages_v_blocks_content = relations(
  _pages_v_blocks_content,
  ({ one, many }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_content._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_content',
    }),
    columns: many(_pages_v_blocks_content_columns, {
      relationName: 'columns',
    }),
  }),
)
export const relations__pages_v_blocks_media_block = relations(
  _pages_v_blocks_media_block,
  ({ one }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_media_block._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_mediaBlock',
    }),
    media: one(media, {
      fields: [_pages_v_blocks_media_block.media],
      references: [media.id],
      relationName: 'media',
    }),
  }),
)
export const relations__pages_v_blocks_archive = relations(_pages_v_blocks_archive, ({ one }) => ({
  _parentID: one(_pages_v, {
    fields: [_pages_v_blocks_archive._parentID],
    references: [_pages_v.id],
    relationName: '_blocks_archive',
  }),
}))
export const relations__pages_v_blocks_form_block = relations(
  _pages_v_blocks_form_block,
  ({ one }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_form_block._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_formBlock',
    }),
    form: one(forms, {
      fields: [_pages_v_blocks_form_block.form],
      references: [forms.id],
      relationName: 'form',
    }),
  }),
)
export const relations__pages_v_rels = relations(_pages_v_rels, ({ one }) => ({
  parent: one(_pages_v, {
    fields: [_pages_v_rels.parent],
    references: [_pages_v.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [_pages_v_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  categoriesID: one(categories, {
    fields: [_pages_v_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  postsID: one(posts, {
    fields: [_pages_v_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
}))
export const relations__pages_v = relations(_pages_v, ({ one, many }) => ({
  parent: one(pages, {
    fields: [_pages_v.parent],
    references: [pages.id],
    relationName: 'parent',
  }),
  version_hero_links: many(_pages_v_version_hero_links, {
    relationName: 'version_hero_links',
  }),
  version_hero_media: one(media, {
    fields: [_pages_v.version_hero_media],
    references: [media.id],
    relationName: 'version_hero_media',
  }),
  _blocks_cta: many(_pages_v_blocks_cta, {
    relationName: '_blocks_cta',
  }),
  _blocks_content: many(_pages_v_blocks_content, {
    relationName: '_blocks_content',
  }),
  _blocks_mediaBlock: many(_pages_v_blocks_media_block, {
    relationName: '_blocks_mediaBlock',
  }),
  _blocks_archive: many(_pages_v_blocks_archive, {
    relationName: '_blocks_archive',
  }),
  _blocks_formBlock: many(_pages_v_blocks_form_block, {
    relationName: '_blocks_formBlock',
  }),
  version_meta_image: one(media, {
    fields: [_pages_v.version_meta_image],
    references: [media.id],
    relationName: 'version_meta_image',
  }),
  _rels: many(_pages_v_rels, {
    relationName: '_rels',
  }),
}))
export const relations_posts_populated_authors = relations(posts_populated_authors, ({ one }) => ({
  _parentID: one(posts, {
    fields: [posts_populated_authors._parentID],
    references: [posts.id],
    relationName: 'populatedAuthors',
  }),
}))
export const relations_posts_rels = relations(posts_rels, ({ one }) => ({
  parent: one(posts, {
    fields: [posts_rels.parent],
    references: [posts.id],
    relationName: '_rels',
  }),
  postsID: one(posts, {
    fields: [posts_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
  categoriesID: one(categories, {
    fields: [posts_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  usersID: one(users, {
    fields: [posts_rels.usersID],
    references: [users.id],
    relationName: 'users',
  }),
}))
export const relations_posts = relations(posts, ({ one, many }) => ({
  featuredImage_media: one(media, {
    fields: [posts.featuredImage_media],
    references: [media.id],
    relationName: 'featuredImage_media',
  }),
  meta_image: one(media, {
    fields: [posts.meta_image],
    references: [media.id],
    relationName: 'meta_image',
  }),
  populatedAuthors: <AUTHORS>
    relationName: 'populatedAuthors',
  }),
  _rels: many(posts_rels, {
    relationName: '_rels',
  }),
}))
export const relations__posts_v_version_populated_authors = relations(
  _posts_v_version_populated_authors,
  ({ one }) => ({
    _parentID: one(_posts_v, {
      fields: [_posts_v_version_populated_authors._parentID],
      references: [_posts_v.id],
      relationName: 'version_populatedAuthors',
    }),
  }),
)
export const relations__posts_v_rels = relations(_posts_v_rels, ({ one }) => ({
  parent: one(_posts_v, {
    fields: [_posts_v_rels.parent],
    references: [_posts_v.id],
    relationName: '_rels',
  }),
  postsID: one(posts, {
    fields: [_posts_v_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
  categoriesID: one(categories, {
    fields: [_posts_v_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  usersID: one(users, {
    fields: [_posts_v_rels.usersID],
    references: [users.id],
    relationName: 'users',
  }),
}))
export const relations__posts_v = relations(_posts_v, ({ one, many }) => ({
  parent: one(posts, {
    fields: [_posts_v.parent],
    references: [posts.id],
    relationName: 'parent',
  }),
  version_featuredImage_media: one(media, {
    fields: [_posts_v.version_featuredImage_media],
    references: [media.id],
    relationName: 'version_featuredImage_media',
  }),
  version_meta_image: one(media, {
    fields: [_posts_v.version_meta_image],
    references: [media.id],
    relationName: 'version_meta_image',
  }),
  version_populatedAuthors: <AUTHORS>
    relationName: 'version_populatedAuthors',
  }),
  _rels: many(_posts_v_rels, {
    relationName: '_rels',
  }),
}))
export const relations_media = relations(media, () => ({}))
export const relations_categories_breadcrumbs = relations(categories_breadcrumbs, ({ one }) => ({
  _parentID: one(categories, {
    fields: [categories_breadcrumbs._parentID],
    references: [categories.id],
    relationName: 'breadcrumbs',
  }),
  doc: one(categories, {
    fields: [categories_breadcrumbs.doc],
    references: [categories.id],
    relationName: 'doc',
  }),
}))
export const relations_categories = relations(categories, ({ one, many }) => ({
  parent: one(categories, {
    fields: [categories.parent],
    references: [categories.id],
    relationName: 'parent',
  }),
  breadcrumbs: many(categories_breadcrumbs, {
    relationName: 'breadcrumbs',
  }),
}))
export const relations_users = relations(users, () => ({}))
export const relations_redirects_rels = relations(redirects_rels, ({ one }) => ({
  parent: one(redirects, {
    fields: [redirects_rels.parent],
    references: [redirects.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [redirects_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  postsID: one(posts, {
    fields: [redirects_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
}))
export const relations_redirects = relations(redirects, ({ many }) => ({
  _rels: many(redirects_rels, {
    relationName: '_rels',
  }),
}))
export const relations_forms_blocks_checkbox = relations(forms_blocks_checkbox, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_checkbox._parentID],
    references: [forms.id],
    relationName: '_blocks_checkbox',
  }),
}))
export const relations_forms_blocks_country = relations(forms_blocks_country, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_country._parentID],
    references: [forms.id],
    relationName: '_blocks_country',
  }),
}))
export const relations_forms_blocks_email = relations(forms_blocks_email, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_email._parentID],
    references: [forms.id],
    relationName: '_blocks_email',
  }),
}))
export const relations_forms_blocks_message = relations(forms_blocks_message, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_message._parentID],
    references: [forms.id],
    relationName: '_blocks_message',
  }),
}))
export const relations_forms_blocks_number = relations(forms_blocks_number, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_number._parentID],
    references: [forms.id],
    relationName: '_blocks_number',
  }),
}))
export const relations_forms_blocks_select_options = relations(
  forms_blocks_select_options,
  ({ one }) => ({
    _parentID: one(forms_blocks_select, {
      fields: [forms_blocks_select_options._parentID],
      references: [forms_blocks_select.id],
      relationName: 'options',
    }),
  }),
)
export const relations_forms_blocks_select = relations(forms_blocks_select, ({ one, many }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_select._parentID],
    references: [forms.id],
    relationName: '_blocks_select',
  }),
  options: many(forms_blocks_select_options, {
    relationName: 'options',
  }),
}))
export const relations_forms_blocks_state = relations(forms_blocks_state, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_state._parentID],
    references: [forms.id],
    relationName: '_blocks_state',
  }),
}))
export const relations_forms_blocks_text = relations(forms_blocks_text, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_text._parentID],
    references: [forms.id],
    relationName: '_blocks_text',
  }),
}))
export const relations_forms_blocks_textarea = relations(forms_blocks_textarea, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_blocks_textarea._parentID],
    references: [forms.id],
    relationName: '_blocks_textarea',
  }),
}))
export const relations_forms_emails = relations(forms_emails, ({ one }) => ({
  _parentID: one(forms, {
    fields: [forms_emails._parentID],
    references: [forms.id],
    relationName: 'emails',
  }),
}))
export const relations_forms = relations(forms, ({ many }) => ({
  _blocks_checkbox: many(forms_blocks_checkbox, {
    relationName: '_blocks_checkbox',
  }),
  _blocks_country: many(forms_blocks_country, {
    relationName: '_blocks_country',
  }),
  _blocks_email: many(forms_blocks_email, {
    relationName: '_blocks_email',
  }),
  _blocks_message: many(forms_blocks_message, {
    relationName: '_blocks_message',
  }),
  _blocks_number: many(forms_blocks_number, {
    relationName: '_blocks_number',
  }),
  _blocks_select: many(forms_blocks_select, {
    relationName: '_blocks_select',
  }),
  _blocks_state: many(forms_blocks_state, {
    relationName: '_blocks_state',
  }),
  _blocks_text: many(forms_blocks_text, {
    relationName: '_blocks_text',
  }),
  _blocks_textarea: many(forms_blocks_textarea, {
    relationName: '_blocks_textarea',
  }),
  emails: many(forms_emails, {
    relationName: 'emails',
  }),
}))
export const relations_form_submissions_submission_data = relations(
  form_submissions_submission_data,
  ({ one }) => ({
    _parentID: one(form_submissions, {
      fields: [form_submissions_submission_data._parentID],
      references: [form_submissions.id],
      relationName: 'submissionData',
    }),
  }),
)
export const relations_form_submissions = relations(form_submissions, ({ one, many }) => ({
  form: one(forms, {
    fields: [form_submissions.form],
    references: [forms.id],
    relationName: 'form',
  }),
  submissionData: many(form_submissions_submission_data, {
    relationName: 'submissionData',
  }),
}))
export const relations_search_categories = relations(search_categories, ({ one }) => ({
  _parentID: one(search, {
    fields: [search_categories._parentID],
    references: [search.id],
    relationName: 'categories',
  }),
}))
export const relations_search_rels = relations(search_rels, ({ one }) => ({
  parent: one(search, {
    fields: [search_rels.parent],
    references: [search.id],
    relationName: '_rels',
  }),
  postsID: one(posts, {
    fields: [search_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
}))
export const relations_search = relations(search, ({ one, many }) => ({
  meta_image: one(media, {
    fields: [search.meta_image],
    references: [media.id],
    relationName: 'meta_image',
  }),
  categories: many(search_categories, {
    relationName: 'categories',
  }),
  _rels: many(search_rels, {
    relationName: '_rels',
  }),
}))
export const relations_payload_jobs_log = relations(payload_jobs_log, ({ one }) => ({
  _parentID: one(payload_jobs, {
    fields: [payload_jobs_log._parentID],
    references: [payload_jobs.id],
    relationName: 'log',
  }),
}))
export const relations_payload_jobs = relations(payload_jobs, ({ many }) => ({
  log: many(payload_jobs_log, {
    relationName: 'log',
  }),
}))
export const relations_payload_locked_documents_rels = relations(
  payload_locked_documents_rels,
  ({ one }) => ({
    parent: one(payload_locked_documents, {
      fields: [payload_locked_documents_rels.parent],
      references: [payload_locked_documents.id],
      relationName: '_rels',
    }),
    pagesID: one(pages, {
      fields: [payload_locked_documents_rels.pagesID],
      references: [pages.id],
      relationName: 'pages',
    }),
    postsID: one(posts, {
      fields: [payload_locked_documents_rels.postsID],
      references: [posts.id],
      relationName: 'posts',
    }),
    mediaID: one(media, {
      fields: [payload_locked_documents_rels.mediaID],
      references: [media.id],
      relationName: 'media',
    }),
    categoriesID: one(categories, {
      fields: [payload_locked_documents_rels.categoriesID],
      references: [categories.id],
      relationName: 'categories',
    }),
    usersID: one(users, {
      fields: [payload_locked_documents_rels.usersID],
      references: [users.id],
      relationName: 'users',
    }),
    redirectsID: one(redirects, {
      fields: [payload_locked_documents_rels.redirectsID],
      references: [redirects.id],
      relationName: 'redirects',
    }),
    formsID: one(forms, {
      fields: [payload_locked_documents_rels.formsID],
      references: [forms.id],
      relationName: 'forms',
    }),
    'form-submissionsID': one(form_submissions, {
      fields: [payload_locked_documents_rels['form-submissionsID']],
      references: [form_submissions.id],
      relationName: 'form-submissions',
    }),
    searchID: one(search, {
      fields: [payload_locked_documents_rels.searchID],
      references: [search.id],
      relationName: 'search',
    }),
    'payload-jobsID': one(payload_jobs, {
      fields: [payload_locked_documents_rels['payload-jobsID']],
      references: [payload_jobs.id],
      relationName: 'payload-jobs',
    }),
  }),
)
export const relations_payload_locked_documents = relations(
  payload_locked_documents,
  ({ many }) => ({
    _rels: many(payload_locked_documents_rels, {
      relationName: '_rels',
    }),
  }),
)
export const relations_payload_preferences_rels = relations(
  payload_preferences_rels,
  ({ one }) => ({
    parent: one(payload_preferences, {
      fields: [payload_preferences_rels.parent],
      references: [payload_preferences.id],
      relationName: '_rels',
    }),
    usersID: one(users, {
      fields: [payload_preferences_rels.usersID],
      references: [users.id],
      relationName: 'users',
    }),
  }),
)
export const relations_payload_preferences = relations(payload_preferences, ({ many }) => ({
  _rels: many(payload_preferences_rels, {
    relationName: '_rels',
  }),
}))
export const relations_payload_migrations = relations(payload_migrations, () => ({}))
export const relations_header_nav_items = relations(header_nav_items, ({ one }) => ({
  _parentID: one(header, {
    fields: [header_nav_items._parentID],
    references: [header.id],
    relationName: 'navItems',
  }),
}))
export const relations_header_rels = relations(header_rels, ({ one }) => ({
  parent: one(header, {
    fields: [header_rels.parent],
    references: [header.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [header_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  categoriesID: one(categories, {
    fields: [header_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  postsID: one(posts, {
    fields: [header_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
}))
export const relations_header = relations(header, ({ many }) => ({
  navItems: many(header_nav_items, {
    relationName: 'navItems',
  }),
  _rels: many(header_rels, {
    relationName: '_rels',
  }),
}))
export const relations_footer_nav_items = relations(footer_nav_items, ({ one }) => ({
  _parentID: one(footer, {
    fields: [footer_nav_items._parentID],
    references: [footer.id],
    relationName: 'navItems',
  }),
}))
export const relations_footer_rels = relations(footer_rels, ({ one }) => ({
  parent: one(footer, {
    fields: [footer_rels.parent],
    references: [footer.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [footer_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  categoriesID: one(categories, {
    fields: [footer_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  postsID: one(posts, {
    fields: [footer_rels.postsID],
    references: [posts.id],
    relationName: 'posts',
  }),
}))
export const relations_footer = relations(footer, ({ many }) => ({
  navItems: many(footer_nav_items, {
    relationName: 'navItems',
  }),
  _rels: many(footer_rels, {
    relationName: '_rels',
  }),
}))
export const relations_home_sections = relations(home_sections, ({ one }) => ({
  _parentID: one(home, {
    fields: [home_sections._parentID],
    references: [home.id],
    relationName: 'sections',
  }),
  category: one(categories, {
    fields: [home_sections.category],
    references: [categories.id],
    relationName: 'category',
  }),
}))
export const relations_home = relations(home, ({ many }) => ({
  sections: many(home_sections, {
    relationName: 'sections',
  }),
}))

type DatabaseSchema = {
  enum_pages_hero_links_link_type: typeof enum_pages_hero_links_link_type
  enum_pages_hero_links_link_appearance: typeof enum_pages_hero_links_link_appearance
  enum_pages_blocks_cta_links_link_type: typeof enum_pages_blocks_cta_links_link_type
  enum_pages_blocks_cta_links_link_appearance: typeof enum_pages_blocks_cta_links_link_appearance
  enum_pages_blocks_content_columns_size: typeof enum_pages_blocks_content_columns_size
  enum_pages_blocks_content_columns_link_type: typeof enum_pages_blocks_content_columns_link_type
  enum_pages_blocks_content_columns_link_appearance: typeof enum_pages_blocks_content_columns_link_appearance
  enum_pages_blocks_archive_populate_by: typeof enum_pages_blocks_archive_populate_by
  enum_pages_blocks_archive_relation_to: typeof enum_pages_blocks_archive_relation_to
  enum_pages_hero_type: typeof enum_pages_hero_type
  enum_pages_status: typeof enum_pages_status
  enum__pages_v_version_hero_links_link_type: typeof enum__pages_v_version_hero_links_link_type
  enum__pages_v_version_hero_links_link_appearance: typeof enum__pages_v_version_hero_links_link_appearance
  enum__pages_v_blocks_cta_links_link_type: typeof enum__pages_v_blocks_cta_links_link_type
  enum__pages_v_blocks_cta_links_link_appearance: typeof enum__pages_v_blocks_cta_links_link_appearance
  enum__pages_v_blocks_content_columns_size: typeof enum__pages_v_blocks_content_columns_size
  enum__pages_v_blocks_content_columns_link_type: typeof enum__pages_v_blocks_content_columns_link_type
  enum__pages_v_blocks_content_columns_link_appearance: typeof enum__pages_v_blocks_content_columns_link_appearance
  enum__pages_v_blocks_archive_populate_by: typeof enum__pages_v_blocks_archive_populate_by
  enum__pages_v_blocks_archive_relation_to: typeof enum__pages_v_blocks_archive_relation_to
  enum__pages_v_version_hero_type: typeof enum__pages_v_version_hero_type
  enum__pages_v_version_status: typeof enum__pages_v_version_status
  enum_posts_featured_image_type: typeof enum_posts_featured_image_type
  enum_posts_status: typeof enum_posts_status
  enum__posts_v_version_featured_image_type: typeof enum__posts_v_version_featured_image_type
  enum__posts_v_version_status: typeof enum__posts_v_version_status
  enum_redirects_to_type: typeof enum_redirects_to_type
  enum_forms_confirmation_type: typeof enum_forms_confirmation_type
  enum_payload_jobs_log_task_slug: typeof enum_payload_jobs_log_task_slug
  enum_payload_jobs_log_state: typeof enum_payload_jobs_log_state
  enum_payload_jobs_task_slug: typeof enum_payload_jobs_task_slug
  enum_header_nav_items_link_type: typeof enum_header_nav_items_link_type
  enum_footer_nav_items_link_type: typeof enum_footer_nav_items_link_type
  pages_hero_links: typeof pages_hero_links
  pages_blocks_cta_links: typeof pages_blocks_cta_links
  pages_blocks_cta: typeof pages_blocks_cta
  pages_blocks_content_columns: typeof pages_blocks_content_columns
  pages_blocks_content: typeof pages_blocks_content
  pages_blocks_media_block: typeof pages_blocks_media_block
  pages_blocks_archive: typeof pages_blocks_archive
  pages_blocks_form_block: typeof pages_blocks_form_block
  pages: typeof pages
  pages_rels: typeof pages_rels
  _pages_v_version_hero_links: typeof _pages_v_version_hero_links
  _pages_v_blocks_cta_links: typeof _pages_v_blocks_cta_links
  _pages_v_blocks_cta: typeof _pages_v_blocks_cta
  _pages_v_blocks_content_columns: typeof _pages_v_blocks_content_columns
  _pages_v_blocks_content: typeof _pages_v_blocks_content
  _pages_v_blocks_media_block: typeof _pages_v_blocks_media_block
  _pages_v_blocks_archive: typeof _pages_v_blocks_archive
  _pages_v_blocks_form_block: typeof _pages_v_blocks_form_block
  _pages_v: typeof _pages_v
  _pages_v_rels: typeof _pages_v_rels
  posts_populated_authors: typeof posts_populated_authors
  posts: typeof posts
  posts_rels: typeof posts_rels
  _posts_v_version_populated_authors: typeof _posts_v_version_populated_authors
  _posts_v: typeof _posts_v
  _posts_v_rels: typeof _posts_v_rels
  media: typeof media
  categories_breadcrumbs: typeof categories_breadcrumbs
  categories: typeof categories
  users: typeof users
  redirects: typeof redirects
  redirects_rels: typeof redirects_rels
  forms_blocks_checkbox: typeof forms_blocks_checkbox
  forms_blocks_country: typeof forms_blocks_country
  forms_blocks_email: typeof forms_blocks_email
  forms_blocks_message: typeof forms_blocks_message
  forms_blocks_number: typeof forms_blocks_number
  forms_blocks_select_options: typeof forms_blocks_select_options
  forms_blocks_select: typeof forms_blocks_select
  forms_blocks_state: typeof forms_blocks_state
  forms_blocks_text: typeof forms_blocks_text
  forms_blocks_textarea: typeof forms_blocks_textarea
  forms_emails: typeof forms_emails
  forms: typeof forms
  form_submissions_submission_data: typeof form_submissions_submission_data
  form_submissions: typeof form_submissions
  search_categories: typeof search_categories
  search: typeof search
  search_rels: typeof search_rels
  payload_jobs_log: typeof payload_jobs_log
  payload_jobs: typeof payload_jobs
  payload_locked_documents: typeof payload_locked_documents
  payload_locked_documents_rels: typeof payload_locked_documents_rels
  payload_preferences: typeof payload_preferences
  payload_preferences_rels: typeof payload_preferences_rels
  payload_migrations: typeof payload_migrations
  header_nav_items: typeof header_nav_items
  header: typeof header
  header_rels: typeof header_rels
  footer_nav_items: typeof footer_nav_items
  footer: typeof footer
  footer_rels: typeof footer_rels
  home_sections: typeof home_sections
  home: typeof home
  relations_pages_hero_links: typeof relations_pages_hero_links
  relations_pages_blocks_cta_links: typeof relations_pages_blocks_cta_links
  relations_pages_blocks_cta: typeof relations_pages_blocks_cta
  relations_pages_blocks_content_columns: typeof relations_pages_blocks_content_columns
  relations_pages_blocks_content: typeof relations_pages_blocks_content
  relations_pages_blocks_media_block: typeof relations_pages_blocks_media_block
  relations_pages_blocks_archive: typeof relations_pages_blocks_archive
  relations_pages_blocks_form_block: typeof relations_pages_blocks_form_block
  relations_pages_rels: typeof relations_pages_rels
  relations_pages: typeof relations_pages
  relations__pages_v_version_hero_links: typeof relations__pages_v_version_hero_links
  relations__pages_v_blocks_cta_links: typeof relations__pages_v_blocks_cta_links
  relations__pages_v_blocks_cta: typeof relations__pages_v_blocks_cta
  relations__pages_v_blocks_content_columns: typeof relations__pages_v_blocks_content_columns
  relations__pages_v_blocks_content: typeof relations__pages_v_blocks_content
  relations__pages_v_blocks_media_block: typeof relations__pages_v_blocks_media_block
  relations__pages_v_blocks_archive: typeof relations__pages_v_blocks_archive
  relations__pages_v_blocks_form_block: typeof relations__pages_v_blocks_form_block
  relations__pages_v_rels: typeof relations__pages_v_rels
  relations__pages_v: typeof relations__pages_v
  relations_posts_populated_authors: typeof relations_posts_populated_authors
  relations_posts_rels: typeof relations_posts_rels
  relations_posts: typeof relations_posts
  relations__posts_v_version_populated_authors: typeof relations__posts_v_version_populated_authors
  relations__posts_v_rels: typeof relations__posts_v_rels
  relations__posts_v: typeof relations__posts_v
  relations_media: typeof relations_media
  relations_categories_breadcrumbs: typeof relations_categories_breadcrumbs
  relations_categories: typeof relations_categories
  relations_users: typeof relations_users
  relations_redirects_rels: typeof relations_redirects_rels
  relations_redirects: typeof relations_redirects
  relations_forms_blocks_checkbox: typeof relations_forms_blocks_checkbox
  relations_forms_blocks_country: typeof relations_forms_blocks_country
  relations_forms_blocks_email: typeof relations_forms_blocks_email
  relations_forms_blocks_message: typeof relations_forms_blocks_message
  relations_forms_blocks_number: typeof relations_forms_blocks_number
  relations_forms_blocks_select_options: typeof relations_forms_blocks_select_options
  relations_forms_blocks_select: typeof relations_forms_blocks_select
  relations_forms_blocks_state: typeof relations_forms_blocks_state
  relations_forms_blocks_text: typeof relations_forms_blocks_text
  relations_forms_blocks_textarea: typeof relations_forms_blocks_textarea
  relations_forms_emails: typeof relations_forms_emails
  relations_forms: typeof relations_forms
  relations_form_submissions_submission_data: typeof relations_form_submissions_submission_data
  relations_form_submissions: typeof relations_form_submissions
  relations_search_categories: typeof relations_search_categories
  relations_search_rels: typeof relations_search_rels
  relations_search: typeof relations_search
  relations_payload_jobs_log: typeof relations_payload_jobs_log
  relations_payload_jobs: typeof relations_payload_jobs
  relations_payload_locked_documents_rels: typeof relations_payload_locked_documents_rels
  relations_payload_locked_documents: typeof relations_payload_locked_documents
  relations_payload_preferences_rels: typeof relations_payload_preferences_rels
  relations_payload_preferences: typeof relations_payload_preferences
  relations_payload_migrations: typeof relations_payload_migrations
  relations_header_nav_items: typeof relations_header_nav_items
  relations_header_rels: typeof relations_header_rels
  relations_header: typeof relations_header
  relations_footer_nav_items: typeof relations_footer_nav_items
  relations_footer_rels: typeof relations_footer_rels
  relations_footer: typeof relations_footer
  relations_home_sections: typeof relations_home_sections
  relations_home: typeof relations_home
}

declare module '@payloadcms/db-postgres' {
  export interface GeneratedDatabaseSchema {
    schema: DatabaseSchema
  }
}
