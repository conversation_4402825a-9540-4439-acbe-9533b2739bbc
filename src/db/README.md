# Seed Assets Directory

This directory is used for storing media files that will be used during the database seeding process.

## How to Use

1. Place your image files in this directory (supported formats: jpg, jpeg, png, gif, webp)
2. Run the seeding command: `npm run db:seed` or `pnpm db:seed`

**Note:** Currently, automatic media seeding is disabled due to Node.js limitations with the Payload file upload system. You will need to manually upload images through the admin interface.

## Recommended Image Types

- Hero images for posts (recommended size: 1200x630px)
- Thumbnails (recommended size: 400x400px)
- Logo images (recommended size: 200x200px)

## File Naming Conventions

Use descriptive file names as they will be used for the image alt text during seeding.

Example:
- `tech-innovation-summit.jpg`
- `macau-skyline.png`
- `economy-report.webp` 