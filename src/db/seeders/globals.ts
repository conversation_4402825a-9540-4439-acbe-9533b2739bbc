import { Payload } from 'payload'

export const seedGlobals = async (payload: Payload): Promise<void> => {
  console.log('🌱 Seeding global...')

  const categories = await payload.find({
    collection: 'categories',
    limit: 6,
    depth: 1,
    pagination: false,
    select: { id: true, title: true, slug: true },
  })

  if (!categories) {
    console.error('No categories found')
    return
  }

  await payload.updateGlobal({
    slug: 'home',
    data: {
      sections: categories.docs.map((category) => ({
        title: category.title,
        category: category.id,
      })),
    },
  })

  await payload.updateGlobal({
    slug: 'header',
    data: {
      navItems: [
        ...categories.docs.map((category) => ({
          link: {
            type: 'reference' as const,
            newTab: false,
            label: category.title,
            reference: {
              relationTo: 'categories' as const,
              value: category.id,
            },
          },
        })),
        {
          link: {
            type: 'custom',
            label: '更多文章',
            newTab: false,
            url: '/posts',
          },
        },
      ],
    },
  })

  console.log(`✅ Successfully seeded global`)
}
