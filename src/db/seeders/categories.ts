import { Payload } from 'payload'
import { Category } from '../../payload-types'

type CategorySeed = {
  title: string
  slug: string
  description?: string
}

export const initialCategories: CategorySeed[] = [
  {
    title: '澳門科技',
    slug: 'tech',
    description: '澳門本地科技新聞、研發創新、本地科技企業動態',
  },
  {
    title: '科技與創新',
    slug: 'international-tech-innovation',
    description: '國際科技新趨勢、全球創新動態、科技企業國際新聞',
  },
  {
    title: '經濟與產業',
    slug: 'economy',
    description: '澳門本地經濟趨勢、產業發展、金融與市場分析',
  },
  {
    title: '政府與政策',
    slug: 'politics',
    description: '澳門政府公告、政策法規解讀、公共政策分析',
  },
  {
    title: '社會民生',
    slug: 'society',
    description: '社會議題、就業與民生、社區發展',
  },
  {
    title: '健康醫療',
    slug: 'health',
    description: '公共衛生、醫療科技、健康保健資訊',
  },
  {
    title: '教育學習',
    slug: 'education',
    description: '校園新聞、教育政策、學習資源',
  },
]

export const seedCategories = async (payload: Payload): Promise<void> => {
  console.log('🌱 Seeding categories...')

  const createdCategories: Category[] = []

  for (const category of initialCategories) {
    try {
      // We'll only use title and slug from the provided data
      // description will be ignored as it's not part of the Category schema
      const createdCategory = await payload.create({
        collection: 'categories',
        data: {
          title: category.title,
          slug: category.slug,
        },
      })

      createdCategories.push(createdCategory)
    } catch (error) {
      console.error(`❌ Error creating category "${category.title}":`, error)
    }
  }

  console.log(`✅ Successfully seeded ${createdCategories.length} categories`)
}
