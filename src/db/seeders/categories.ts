import { Payload } from 'payload'
import { Category } from '../../payload-types'

type CategorySeed = {
  title: string
  slug: string
  description?: string
}

export const initialCategories: CategorySeed[] = [
  {
    title: '澳門新聞',
    slug: 'macau-news',
    description:
      '澳門本地重要新聞及突發事件，包括交通狀況、治安事件、社區活動、本地人物專訪等與市民日常生活息息相關的資訊。',
  },
  {
    title: '澳門科技',
    slug: 'macau-tech',
    description: '澳門本地科技發展動態、創新研發項目、科技企業資訊及數字化轉型相關新聞。',
  },
  {
    title: '政府政策',
    slug: 'government-policy',
    description:
      '澳門特區政府最新政策公告、法規修訂、施政措施、官員動態及立法會議事等政府相關資訊。',
  },
  {
    title: '經濟財經',
    slug: 'economy',
    description: '澳門經濟發展、博彩業動態、旅遊市場分析、房地產資訊及金融投資相關新聞。',
  },
  {
    title: '社會民生',
    slug: 'society',
    description: '關注市民生活品質、社會福利、住屋問題、就業狀況等影響民生的社會議題。',
  },
  {
    title: '國際新聞',
    slug: 'international-news',
    description: '重要國際時事、全球經濟動向、國際關係及對澳門有影響的國際新聞。',
  },
  {
    title: '體育新聞',
    slug: 'sports',
    description: '本地及國際體育賽事報導、運動員動態、體育設施發展及全民健身資訊。',
  },
  {
    title: '文化生活',
    slug: 'culture-lifestyle',
    description: '澳門文化活動、藝術展覽、美食推介、旅遊景點及市民休閒生活相關資訊。',
  },
  {
    title: '科技新聞',
    slug: 'technology',
    description: '全球科技創新趨勢、新產品發布、科技應用及數碼生活相關資訊。',
  },
  {
    title: '大灣區新聞',
    slug: 'greater-bay-area',
    description:
      '粵港澳大灣區發展資訊，包括跨境合作項目、區域政策、交通建設、人才交流及經濟一體化進程。',
  },
  {
    title: '健康醫療',
    slug: 'health',
    description: '公共衛生資訊、醫療服務動態、健康生活貼士及疾病預防相關知識。',
  },
  {
    title: '教育學習',
    slug: 'education',
    description: '教育政策更新、學校活動、升學資訊、職業培訓及終身學習相關內容。',
  },
]

export const seedCategories = async (payload: Payload): Promise<void> => {
  console.log('🌱 Seeding categories...')

  const createdCategories: Category[] = []

  for (const category of initialCategories) {
    try {
      const createdCategory = await payload.create({
        collection: 'categories',
        data: {
          title: category.title,
          slug: category.slug,
        },
      })

      createdCategories.push(createdCategory)
    } catch (error) {
      console.error(`❌ Error creating category "${category.title}":`, error)
    }
  }

  console.log(`✅ Successfully seeded ${createdCategories.length} categories`)
}
