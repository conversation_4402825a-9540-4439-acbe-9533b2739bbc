import { Payload } from 'payload'
import configPromise from '@/payload.config'
import { convertMarkdownToLexical, editorConfigFactory } from '@payloadcms/richtext-lexical'

import postsData from './assests/posts.json'
import { Post } from '@/payload-types'

export interface PostSeedData {
  title: string
  slug: string
  content: string
  categories: string[]
  seo_title: string
  seo_description: string
  published_at: string
}

const config = await configPromise

export const seedPosts = async (payload: Payload): Promise<void> => {
  console.log('🌱 Seeding posts...')

  if (postsData.length === 0) {
    console.log('⚠️ No posts data found. Skipping posts seeding.')
    return
  }

  try {
    // Get all categories for linking
    const categoriesResult = await payload.find({
      collection: 'categories',
      limit: 10,
      depth: 1,
    })

    const categoryMap: Record<string, number> = {}
    categoriesResult.docs.forEach((category) => {
      if (category.slug) {
        categoryMap[category.slug] = category.id
      }
    })

    // Get some media for hero images
    const mediaResult = await payload.find({
      collection: 'media',
      limit: 10,
    })

    if (mediaResult.docs.length === 0) {
      console.log('⚠️ No media found. Posts will be created without hero images.')
    }

    let createdCount = 0

    for (const [index, post] of postsData.entries()) {
      try {
        // Check if post already exists by slug
        const existingPosts = await payload.find({
          collection: 'posts',
          where: {
            slug: {
              equals: post.slug,
            },
          },
        })

        if (existingPosts.docs.length > 0) {
          console.log(`ℹ️ Post "${post.title}" already exists, skipping...`)
          continue
        }

        // Convert markdown to Lexical
        const content = convertMarkdownToLexical({
          editorConfig: await editorConfigFactory.default({
            config,
          }),
          markdown: post.content,
        })

        // Prepare post data with the correct typing
        const postData: Omit<Post, 'id' | 'updatedAt' | 'createdAt'> = {
          title: post.title,
          slug: post.slug,
          content: content as Post['content'],
          publishedAt: new Date(post.published_at).toISOString(),
          _status: 'published',
          meta: {
            title: post.seo_title,
            description: post.seo_description,
          },
        }

        // Add categories if they exist
        if (post.categories && post.categories.length > 0) {
          const categoryIds = post.categories
            .map((slug) => categoryMap[slug])
            .filter((id) => id !== undefined)

          if (categoryIds.length > 0) {
            postData.categories = categoryIds
          }
        }

        // Add a featured image if available
        if (mediaResult.docs.length > 0) {
          // Cycle through available media
          const mediaIndex = index % mediaResult.docs.length
          const media = mediaResult.docs[mediaIndex]
          if (media && media.id) {
            postData.featuredImage = {
              type: 'upload',
              media: media.id,
              alt: `Featured image for ${postData.title}`,
            }
          }
        }

        // Create the post
        await payload.create({
          collection: 'posts',
          data: postData,
          overrideAccess: true,
        })

        createdCount++
      } catch (postError: any) {
        // @ts-ignore @typescript-eslint/no-explicit-any
        // Log error but continue with other posts
        console.error(`❌ Error creating post "${post.title}":`, postError.message || postError)
      }
    }

    console.log(`✅ Successfully seeded ${createdCount} posts`)
  } catch (error) {
    console.error(`❌ Error seeding posts:`, error)
  }
}
