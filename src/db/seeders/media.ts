import { Payload } from 'payload'
import path from 'path'
import fs from 'fs'
import { fileURLToPath } from 'url'

const __dirname = fileURLToPath(import.meta.url)

export const seedMedia = async (payload: Payload): Promise<void> => {
  console.log('🌱 Seeding media...')

  try {
    // Path to seed images (adjust this to your project structure)
    const seedImagesPath = path.resolve(__dirname, '../../../seed-assets')

    // Check if the directory exists
    if (!fs.existsSync(seedImagesPath)) {
      console.log(`⚠️ Seed images directory not found: ${seedImagesPath}`)
      console.log('⚠️ Skipping media seeding. To seed media, create the directory and add images.')
      return
    }

    // Get list of image files
    const files = fs.readdirSync(seedImagesPath)
    const imageFiles = files.filter((file) => {
      const ext = path.extname(file).toLowerCase()
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)
    })

    if (imageFiles.length === 0) {
      console.log('⚠️ No image files found in seed directory. Skipping media seeding.')
      return
    }

    console.log(`📸 Found ${imageFiles.length} images to seed.`)

    console.log('⚠️ Note: Media seeding via direct file upload is not implemented in this version.')
    console.log('⚠️ Please upload media files manually through the admin interface.')
    console.log(
      "⚠️ This limitation is due to Payload's file upload system requiring browser File objects.",
    )

    /* 
    // This code would work in a browser environment but not in Node.js directly
    // For a complete implementation, you would need to use something like FormData and node-fetch
    
    // Seed each image
    for (const file of imageFiles) {
      const filePath = path.join(seedImagesPath, file)
      const fileName = path.parse(file).name
      
      await payload.create({
        collection: 'media',
        data: {
          alt: fileName,
        },
        file: fileObj, // This requires a browser File object which isn't available in Node
      })
    }
    */
  } catch (error) {
    console.error(`❌ Error seeding media:`, error)
  }
}
