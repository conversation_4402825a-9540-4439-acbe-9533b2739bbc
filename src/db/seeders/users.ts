import { Payload } from 'payload'

// Admin user credentials
const adminUser = {
  email: '<EMAIL>',
  password: 'Hnscwfd1YhcjpTLG',
  name: 'Admin User',
}

export const seedUsers = async (payload: Payload): Promise<void> => {
  console.log('🌱 Seeding users...')

  try {
    // Check if admin user already exists
    const existingAdmin = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: adminUser.email,
        },
      },
    })

    if (existingAdmin.docs.length > 0) {
      console.log(`✅ Admin user already exists, updating...`)
      await payload.update({
        collection: 'users',
        data: {
          password: adminUser.password,
          name: adminUser.name,
        },
        where: {
          email: {
            equals: adminUser.email,
          },
        },
      })
      console.log(`✅ Successfully updated admin user: ${adminUser.email}`)
      return
    }

    // Create admin user
    const createdUser = await payload.create({
      collection: 'users',
      data: adminUser,
    })

    console.log(`✅ Successfully created admin user: ${createdUser.email}`)
  } catch (error) {
    console.error(`❌ Error creating admin user:`, error)
  }
}
