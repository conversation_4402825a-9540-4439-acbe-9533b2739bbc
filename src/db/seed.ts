import { getPayload, Payload } from 'payload'
import config from '../payload.config'
import { seedCategories } from '@/db/seeders/categories'
import { seedUsers } from '@/db/seeders/users'
import { seedMedia } from '@/db/seeders/media'
import { seedPosts } from '@/db/seeders/posts'
import { seedGlobals } from './seeders/globals'

async function seed() {
  console.log('🌱 Seeding database...')

  const payload = await getPayload({ config })

  // Clear collections before seeding
  console.log('🧹 Clearing existing collections...')
  await clearCollections(payload)

  // Run seeders
  console.log('🔄 Running seeders...')

  await seedUsers(payload)
  await seedCategories(payload)
  await seedMedia(payload)
  await seedPosts(payload)
  await seedGlobals(payload)

  console.log('✅ Database seeding completed successfully!')
}

async function clearCollections(payload: Payload) {
  try {
    await payload.delete({
      collection: 'users',
      where: {},
    })
    await payload.updateGlobal({
      slug: 'home',
      data: {
        sections: [],
      },
      overrideAccess: true,
      overrideLock: true,
    })
    await payload.updateGlobal({
      slug: 'header',
      data: {
        navItems: [],
      },
    })
    await payload.delete({ collection: 'posts', where: {} })
    await payload.delete({ collection: 'categories', where: {} })
    await payload.delete({ collection: 'media', where: {} })

    console.log('✅ Collections cleared successfully')
  } catch (error) {
    console.error('❌ Error clearing collections:', error)
  }
}

seed()
  .then(() => {
    console.log('🎉 All done! Exiting...')
    process.exit(0)
  })
  .catch((err) => {
    console.error('❌ Error during seeding:', err)
    process.exit(1)
  })
