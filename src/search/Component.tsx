import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import React from 'react'
import { SearchIcon } from 'lucide-react'

export type Props = {
  initialValue?: string
}

export const Search: React.FC<Props> = ({ initialValue = '' }) => {
  return (
    <div>
      <form action="/search" method="get" className="relative flex items-center">
        <Label htmlFor="search" className="sr-only">
          搜尋新聞
        </Label>
        <Input
          id="search"
          name="q"
          defaultValue={initialValue}
          placeholder="搜尋新聞..."
          className="pr-10"
        />
        <button
          type="submit"
          className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
          aria-label="Search"
        >
          <SearchIcon className="w-5 h-5 text-gray-500 hover:text-blue-500 transition-colors" />
        </button>
      </form>
    </div>
  )
}
