import { BlobServiceClient } from '@azure/storage-blob'

const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING!
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME!
const baseUrl = process.env.AZURE_STORAGE_BASE_URL!

if (!connectionString) {
  throw new Error('AZURE_STORAGE_CONNECTION_STRING environment variable is required')
}

if (!containerName) {
  throw new Error('AZURE_STORAGE_CONTAINER_NAME environment variable is required')
}

const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString)

/**
 * Upload a buffer to Azure Blob Storage
 * @param buffer - The file buffer to upload
 * @param fileName - The name of the file
 * @param contentType - The MIME type of the file
 * @returns The URL of the uploaded blob
 */
export const uploadToBlob = async (
  buffer: Buffer,
  fileName: string,
  contentType: string,
): Promise<string> => {
  try {
    const containerClient = blobServiceClient.getContainerClient(containerName)
    const blockBlobClient = containerClient.getBlockBlobClient(fileName)

    await blockBlobClient.upload(buffer, buffer.length, {
      blobHTTPHeaders: { blobContentType: contentType },
    })

    // Use custom base URL if provided, otherwise use the default blob URL
    return baseUrl ? `${baseUrl}/${containerName}/${fileName}` : blockBlobClient.url
  } catch (error) {
    console.error('Error uploading to Azure Blob Storage:', error)
    throw new Error(`Failed to upload file to Azure Blob Storage: ${error}`)
  }
}

/**
 * Generate a unique filename with timestamp prefix
 * @param extension - The file extension (with or without dot)
 * @param prefix - Optional prefix for the filename
 * @returns A unique filename
 */
export const generateUniqueFilename = (extension: string, prefix: string = 'image'): string => {
  const timestamp = Date.now()
  const randomSuffix = Math.random().toString(36).substring(2, 8)
  const cleanExtension = extension.startsWith('.') ? extension : `.${extension}`

  return `${timestamp}-${randomSuffix}-${prefix}${cleanExtension}`
}

/**
 * Get content type from filename extension
 * @param filename - The filename
 * @returns The MIME type
 */
export const getContentTypeFromFilename = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase()

  const mimeTypes: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    bmp: 'image/bmp',
    ico: 'image/x-icon',
  }

  return mimeTypes[extension || ''] || 'application/octet-stream'
}

/**
 * Extract file extension from base64 data URL
 * @param base64Data - Base64 data URL (e.g., "data:image/jpeg;base64,...")
 * @returns File extension (e.g., "jpg", "png")
 */
export const getExtensionFromBase64 = (base64Data: string): string => {
  // Extract MIME type from data URL
  const mimeMatch = base64Data.match(/^data:image\/([a-z]+);base64,/)
  if (mimeMatch && mimeMatch[1]) {
    const mimeType = mimeMatch[1].toLowerCase()
    // Convert MIME type to file extension
    const extensionMap: Record<string, string> = {
      jpeg: 'jpg',
      jpg: 'jpg',
      png: 'png',
      gif: 'gif',
      webp: 'webp',
      svg: 'svg',
      bmp: 'bmp',
    }
    return extensionMap[mimeType] || 'jpg' // Default to jpg if unknown
  }

  // Default to jpg if no MIME type found
  return 'jpg'
}

/**
 * Convert base64 string to buffer
 * @param base64Data - Base64 encoded string (with or without data URL prefix)
 * @returns Buffer containing the decoded data
 */
export const base64ToBuffer = (base64Data: string): Buffer => {
  // Remove data URL prefix if present (e.g., "data:image/jpeg;base64,")
  const cleanBase64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '')
  return Buffer.from(cleanBase64, 'base64')
}
