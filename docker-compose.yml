services:
  # payload:
  #   image: node:18-alpine
  #   ports:
  #     - '3000:3000'
  #   volumes:
  #     - .:/home/<USER>/app
  #     - node_modules:/home/<USER>/app/node_modules
  #   working_dir: /home/<USER>/app/
  #   command: sh -c "yarn install && yarn dev"
  #   depends_on:
  #     - postgres
  #   env_file:
  #     - .env

  postgres:
    restart: always
    image: postgres:17
    ports:
      - "5433:5432"
    volumes:
      - data:/var/lib/postgresql/data
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}

volumes:
  data:
  # node_modules:
