# ─── Post<PERSON><PERSON> (Docker) ───────────────────────────
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_DB=

# ─── Payload app config ───────────────────────────

# Database connection string
DATABASE_URI=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@<SET_YOUR_URL>/${POSTGRES_DB}

# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=

# Used to encrypt JWT tokens
PAYLOAD_SECRET=

# Azure Blob
AZURE_STORAGE_BASE_URL=
AZURE_STORAGE_CONNECTION_STRING=
AZURE_STORAGE_CONTAINER_NAME=

# Used to validate preview requests - Random string
PREVIEW_SECRET=

# Secret used to authenticate cron jobs - Not used for now
CRON_SECREt=