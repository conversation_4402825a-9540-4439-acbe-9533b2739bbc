# 內部 Posts 建立 API 使用說明

以下說明如何呼叫內部專用的「建立文章」API，並提供範例與已預設可使用的分類清單。

> **注意**：目前此 API 版本主要用於測試自動化文章發佈流程，後續將增強更多功能，如文章圖片上傳與管理。若有特殊需求，請聯絡開發團隊。

---

## 一、基本資訊

- **Base URL**：  
  `/api/v1/posts`

- **認證方式**：  
  請於 HTTP headers 加入：
  ```
  Authorization: Bearer press-site-api-token-12345
  Content-Type: application/json
  ```

- **HTTP Method**：  
  `POST`

---

## 二、Request Body 格式

```ts
// CategoryInput 定義：
// - 可以只傳 title (string) - 分類的中文名稱
// - 或傳 { title: string; slug?: string } - 如果需要指定特定的 slug
export type CategoryInput =
  | string
  | { title: string; slug?: string }

// 單筆文章資料
export interface PostData {
  title:        string      // 文章標題
  slug:         string      // URL 友好的唯一識別碼，用於生成文章網址 /posts/{slug}，有助於 SEO，slug 只能是英文和數字，不支援中文
  content:      string      // Markdown 內容
  categories?:  CategoryInput[]  // 分類
  meta?: {
    title?:       string    // SEO 標題，建議在 50-60 字元內，對搜尋引擎排名非常重要
    description?: string    // SEO 描述，建議在 100-150 字元內，會顯示在搜尋結果中
  }
  publishedAt?:  string      // ISO 格式時間，可選，預設為 now()
  _status?:      'draft' | 'published'  // 控制文章的發佈狀態：draft=草稿（不公開顯示），published=已發佈（公開顯示）
}

// 批次建立請求
export interface CreatePostsRequest {
  posts: PostData[]
}
```

### SEO 最佳實踐

根據 [Google 搜尋引擎文檔](https://developers.google.com/search/docs/appearance/snippet#meta-descriptions)，高品質的 meta 標籤能顯著提升搜尋結果中的點擊率：

- **meta.title**：建議控制在 50-60 字元內，應包含相關關鍵詞但避免堆砌
- **meta.description**：建議保持在 100-150 字元內，應準確描述頁面內容，包含相關關鍵詞
  - 每個頁面應有獨特的描述
  - 應包含與頁面相關的重要信息
  - 避免使用通用描述或純關鍵詞列表

## 三、Markdown 支援格式

目前支援的 Markdown 格式包括：

- **標題格式**：使用 `#`、`##`、`###` 等符號
  ```
  # 一級標題
  ## 二級標題
  ### 三級標題
  ```

- **文字格式**：
  ```
  **粗體文字**
  *斜體文字*
  ```

- **列表格式**：
  ```
  - 無序列表項目1
  - 無序列表項目2
    - 子項目
  
  1. 有序列表項目1
  2. 有序列表項目2
  ```

- **其他格式**：
  ```
  > 引用文字
  
  `行內代碼`
  
  ---  (水平分隔線)
  
  [連結文字](https://example.com)
  ```

## 四、Request 範例

### 範例 1：草稿文章（最簡範例）

```js
const body = {
  posts: [
    {
      title: "我的第一篇文章",
      slug: "my-first-post",
      content: "# 標題\n這是一段示範 Markdown 內容。",
      // 不帶 categories、meta、publishedAt → 皆使用預設值 draft
    }
  ]
}

fetch("/api/v1/posts", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "Bearer press-site-api-token-12345",
  },
  body: JSON.stringify(body),
})
  .then(res => res.json())
  .then(data => console.log("建立結果：", data))
  .catch(console.error)
```

### 範例 2：完整已發布文章

```js
const body = {
  posts: [
    {
      title: "五月科技週報",
      slug: "tech-weekly-may-2025",
      content: "## 本週焦點\n- AI 重大突破\n- 新版 JavaScript 框架發佈\n",
      categories: [
        "科技新聞",                           // 使用分類的中文名稱
        { title: "經濟財經", slug: "economy" }  // 或指定特定的 slug
      ],
      meta: {
        title: "2025 年 5 月科技週報",
        description: "本週最重要的科技新聞與市場動態彙整。"
      },
      publishedAt: new Date().toISOString(),
      _status: "published"
    }
  ]
}

fetch("/api/v1/posts", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "Bearer press-site-api-token-12345",
  },
  body: JSON.stringify(body),
})
  .then(res => {
    if (!res.ok) throw new Error("建立失敗")
    return res.json()
  })
  .then(result => console.log("Posts 建立成功：", result))
  .catch(console.error)
```

## 五、Response 範例

### 成功回應

```json
{
  "success": true,
  "message": "Successfully created 1 posts",
  "totalRequested": 1,
  "succeeded": 1,
  "failed": 0,
  "results": [
    {
      "id": 123,
      "title": "五月科技週報",
      "slug": "tech-weekly-may-2025",
      "success": true
    }
  ]
}
```

### 部分成功回應 (HTTP 207)

```json
{
  "success": true,
  "message": "Processed with some failures: 1 succeeded, 1 failed",
  "totalRequested": 2,
  "succeeded": 1,
  "failed": 1,
  "results": [
    {
      "id": 123,
      "title": "五月科技週報",
      "slug": "tech-weekly-may-2025",
      "success": true
    },
    {
      "title": "失敗的文章",
      "slug": "failed-post",
      "success": false,
      "error": "Markdown conversion error: Invalid format"
    }
  ]
}
```

### 錯誤回應

#### 401 Unauthorized
```json
{
  "success": false,
  "error": "Unauthorized"
}
```

#### 400 Bad Request
```json
{
  "success": false,
  "error": "Invalid request format",
  "details": {
    /* Zod validation errors */
  }
}
```

#### 500 Server Error
```json
{
  "success": false,
  "error": "Server error",
  "details": "Error message details"
}
```

## 六、已預設分類清單

以下分類已透過 seed 腳本建立，可直接使用分類名稱（title）或同時提供 title+slug。

| 類別名稱         | slug                         | 描述                                           |
| --------------- | ---------------------------- | ---------------------------------------------- |
| 澳門新聞         | macau-news                   | 澳門本地發生的重要新聞事件、突發事件、市民生活相關新聞、本地人物專訪、澳門獨有的社會現象或事件。包括但不限於：交通意外、火災、治安事件、市民投訴、本地商業活動、社區活動。 |
| 政府政策         | government-policy            | 澳門特別行政區政府發布的公告、政策法規、施政報告、政府部門動態、官員任免、立法會議事、政策解讀分析、政府與市民互動相關新聞。 |
| 經濟財經         | economy                      | 經濟、博彩、旅遊、地產                           |
| 社會民生         | society                      | 民生問題、社會議題                              |
| 國際新聞         | international-news           | 國際要聞                                        |
| 體育新聞         | sports                       | 體育賽事                                        |
| 文化生活         | culture-lifestyle            | 文化、娛樂、美食、生活                           |
| 科技新聞         | technology                   | 科技創新（如果科技新聞較多）                      |
| 大灣區新聞       | greater-bay-area             | 粵港澳大灣區發展相關新聞，包括：跨境合作項目、大灣區政策、港珠澳大橋相關、跨境交通、區域經濟合作、人才流動、基建發展、區域一體化進程。 |
| 健康醫療         | health                       | 公共衛生、醫療科技、健康保健資訊                  |
| 教育學習         | education                    | 校園新聞、教育政策、學習資源                      |

## 七、功能說明

### 自動 slug 生成

若提供的 slug 已存在，API 會自動在 slug 後附加數字後綴以確保唯一性。例如：
- 若 `my-post` 已存在，系統會自動生成 `my-post-1`
- 若 `my-post-1` 也已存在，則會生成 `my-post-2`，以此類推

### 自動分類創建

若提供的分類名稱（title）不存在，系統會自動創建該分類：
- 若僅提供 title 字串，系統會自動生成隨機 slug
- 若提供對象 `{ title, slug }`，則使用指定的 slug
- 工作人員可稍後手動更新自動生成的 slug

### Markdown 轉換

API 會自動將 Markdown 格式轉換為系統內部使用的 Lexical 格式。不支援的格式可能導致轉換錯誤。
