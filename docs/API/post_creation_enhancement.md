# Post Creation with Image Upload Enhancement

## Overview

This document outlines best practices for enhancing the Post API creation endpoint to support image uploads alongside extended post metadata. The goal is to create a robust and scalable solution for managing content and associated media.

The implementation supports two image handling approaches:
1. **Base64 Image Upload**: Images are sent as base64-encoded strings and stored on the server
2. **External Image URLs**: Images are referenced via external URLs without server storage

## Current Implementation

The current post creation API (`/api/v1/posts/route.ts`) handles the creation of posts with a core set of fields. This guide details how to expand this to include image uploads and additional tracking fields.

### Updated Post Schema

To accommodate the new requirements, the post data structure will be updated to include fields for tracking the source and other metadata, as well as image handling.

Here is the new Zod schema for the post data:

```typescript
const PostSchema = z.object({
  title: z.string(),
  slug: z.string(),
  content: z.string(), // Markdown content
  summary: z.string().optional(), // new
  source_url: z.string().url().optional(), // new
  source_site_name: z.string().optional(), // new
  extra: z.record(z.any()).optional(), // new， For arbitrary JSON metadata
  categories: z.array(CategoryInputSchema).optional(),
  meta: z
    .object({
      title: z.string().optional(),
      description: z.string().optional(),
    })
    .optional(),
  publishedAt: z.string().datetime().optional(),
  _status: z.enum(['draft', 'published']).optional().default('draft'),
  // Image handling fields
  featuredImage: z.object({
    type: z.enum(['base64', 'external']),
    data: z.string(), // Base64 string or external URL
    alt: z.string().optional(),
  }).optional(),
});
```

## Azure Blob Storage Configuration

Since we're using base64 uploads and storing images in Azure Blob Storage, here's the configuration needed:

### Install Required Dependencies

```bash
npm install @azure/storage-blob
```

### Environment Variables

Add these to your `.env` file:

```env
AZURE_STORAGE_BASE_URL=
AZURE_STORAGE_CONNECTION_STRING=
AZURE_STORAGE_CONTAINER_NAME=
```

### Azure Blob Service Setup

```typescript
// lib/azure-blob.ts
import { BlobServiceClient } from '@azure/storage-blob';

const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING!;
const containerName = process.env.AZURE_STORAGE_CONTAINER_NAME!;
const baseUrl = process.env.AZURE_STORAGE_BASE_URL!;

const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);

export const uploadToBlob = async (
  buffer: Buffer,
  fileName: string,
  contentType: string
): Promise<string> => {
  const containerClient = blobServiceClient.getContainerClient(containerName);
  const blockBlobClient = containerClient.getBlockBlobClient(fileName);

  await blockBlobClient.upload(buffer, buffer.length, {
    blobHTTPHeaders: { blobContentType: contentType }
  });

  // Use custom base URL if provided, otherwise use the default blob URL
  return baseUrl ? `${baseUrl}/${containerName}/${fileName}` : blockBlobClient.url;
};
```

## Unified API Implementation

The API has been designed as a single endpoint that intelligently handles both single posts and batch requests.

**Endpoint:** `POST /api/v1/posts`

### Single Post Request
Send a single post object directly:

```typescript
// Single post request
const singlePostData = {
  title: "My Article",
  slug: "my-article",
  content: "Article content...",
  featuredImage: {
    type: "base64",
    data: "data:image/jpeg;base64,...",
    alt: "Article image"
  }
};

const response = await fetch('/api/v1/posts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer press-site-api-token-12345'
  },
  body: JSON.stringify(singlePostData)
});
```

### Batch Request
Send an object with a `posts` array:

```typescript
// Batch request
const batchData = {
  posts: [
    {
      title: "Article 1",
      slug: "article-1",
      content: "Content 1...",
      featuredImage: { type: "external", data: "https://example.com/img1.jpg" }
    },
    {
      title: "Article 2",
      slug: "article-2",
      content: "Content 2..."
    }
  ]
};

const response = await fetch('/api/v1/posts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer press-site-api-token-12345'
  },
  body: JSON.stringify(batchData)
});
```

### API Implementation Details

```typescript
// In /api/v1/posts/route.ts

export async function POST(request: NextRequest) {
  // 1. Verify authentication
  // ...

  // 2. Parse the JSON request body
  const body = await request.json();
  const parseResult = RequestSchema.safeParse(body);

  // 3. Determine if single post or batch
  if ('posts' in parseResult.data) {
    // Handle batch processing
    const { posts } = parseResult.data;
    // Process each post...
  } else {
    // Handle single post
    const postData = parseResult.data;
    // Process single post...
  }

  // 4. Handle the featured image if it exists
  let featuredImageId;

  if (postData.featuredImage) {
    const { type, data, filename, alt } = postData.featuredImage;

    if (type === 'base64') {
      // Handle base64 image upload to Azure Blob Storage
      if (!filename) {
        return NextResponse.json({
          success: false,
          error: 'Filename is required for base64 images'
        }, { status: 400 });
      }

      // Convert base64 to buffer
      const base64Data = data.replace(/^data:image\/[a-z]+;base64,/, '');
      const buffer = Buffer.from(base64Data, 'base64');

      // Generate unique filename
      const uniqueFilename = `${Date.now()}-${filename}`;
      const contentType = `image/${filename.split('.').pop()}`;

      // Upload to Azure Blob Storage
      const blobUrl = await uploadToBlob(buffer, uniqueFilename, contentType);

      // Create media entry in Payload with blob URL
      const media = await payload.create({
        collection: 'media',
        data: {
          alt: alt || postData.title,
          url: blobUrl,
          filename: uniqueFilename,
        },
        overrideAccess: true,
      });
      featuredImageId = media.id;

    } else if (type === 'external') {
      // Handle external image URL
      const media = await payload.create({
        collection: 'media',
        data: {
          alt: alt || postData.title,
          url: data,
        },
        overrideAccess: true,
      });
      featuredImageId = media.id;
    }
  }

  // 5. Prepare the final data for post creation
  const { featuredImage, ...postDataWithoutImage } = postData;
  const createPostData = {
    ...postDataWithoutImage,
    featuredImage: featuredImageId, // Add the relationship to the media
  };

  // 6. Create the post in the 'posts' collection
  const post = await payload.create({
    collection: 'posts',
    data: createPostData,
    overrideAccess: true,
  });

  // 7. Return the response
  return NextResponse.json({
    success: true,
    post: post
  });
}
```

## Response Formats

### Single Post Response
```json
{
  "success": true,
  "post": {
    "id": 123,
    "title": "My Article",
    "slug": "my-article"
  }
}
```

### Batch Response
```json
{
  "success": true,
  "message": "Successfully created 2 posts",
  "totalRequested": 2,
  "succeeded": 2,
  "failed": 0,
  "results": [
    {
      "id": 124,
      "title": "Article 1",
      "slug": "article-1",
      "success": true
    },
    {
      "id": 125,
      "title": "Article 2",
      "slug": "article-2",
      "success": true
    }
  ]
}
```

## Helper Functions

### Convert File to Base64
```typescript
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};
```

### Process Articles in Batches
```typescript
const processBatch = async (articles, batchSize = 15) => {
  const results = [];

  for (let i = 0; i < articles.length; i += batchSize) {
    const batch = articles.slice(i, i + batchSize);

    try {
      const response = await fetch('/api/v1/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AUTH_TOKEN}`,
        },
        body: JSON.stringify({ posts: batch }),
      });

      const result = await response.json();
      results.push(result);

      // Optional: Add delay between batches
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error(`Batch ${i / batchSize + 1} failed:`, error);
    }
  }

  return results;
};
```

## Media Collection Configuration

Configure your Payload media collection to work with Azure Blob Storage URLs:

```typescript
// In your Payload config
const Media: CollectionConfig = {
  slug: 'media',
  fields: [
    {
      name: 'alt',
      type: 'text',
    },
    {
      name: 'url',
      type: 'text',
      required: true,
    },
    {
      name: 'filename',
      type: 'text',
    },
  ],
};
```

## Batch Processing for High Volume News

For handling ~100 news articles per day, implementing batch processing is highly recommended to improve performance and reduce server load.

### Recommended Batch API Implementation

Create a separate batch endpoint for processing multiple posts:

```typescript
// In /api/v1/posts/batch/route.ts
export async function POST(request: NextRequest) {
  // 1. Verify authentication
  // ...

  // 2. Parse the batch request
  const { posts } = await request.json();

  if (!Array.isArray(posts) || posts.length === 0) {
    return NextResponse.json({
      success: false,
      error: 'Posts array is required'
    }, { status: 400 });
  }

  // 3. Process posts in batches
  const results = [];
  const errors = [];

  for (const postData of posts) {
    try {
      // Validate each post
      const validationResult = PostSchema.safeParse(postData);
      if (!validationResult.success) {
        errors.push({
          post: postData.title || 'Unknown',
          error: validationResult.error.errors
        });
        continue;
      }

      // Handle image processing (same logic as single post)
      let featuredImageId;
      if (validationResult.data.featuredImage) {
        const { type, data, filename, alt } = validationResult.data.featuredImage;

        if (type === 'base64' && filename) {
          const base64Data = data.replace(/^data:image\/[a-z]+;base64,/, '');
          const buffer = Buffer.from(base64Data, 'base64');
          const uniqueFilename = `${Date.now()}-${filename}`;
          const contentType = `image/${filename.split('.').pop()}`;

          const blobUrl = await uploadToBlob(buffer, uniqueFilename, contentType);

          const media = await payload.create({
            collection: 'media',
            data: { alt: alt || validationResult.data.title, url: blobUrl, filename: uniqueFilename },
            overrideAccess: true,
          });
          featuredImageId = media.id;

        } else if (type === 'external') {
          const media = await payload.create({
            collection: 'media',
            data: { alt: alt || validationResult.data.title, url: data },
            overrideAccess: true,
          });
          featuredImageId = media.id;
        }
      }

      // Create the post
      const post = await payload.create({
        collection: 'posts',
        data: {
          ...validationResult.data,
          featuredImage: featuredImageId,
        },
        overrideAccess: true,
      });

      results.push({ success: true, post: post });

    } catch (error) {
      errors.push({
        post: postData.title || 'Unknown',
        error: error.message
      });
    }
  }

  return NextResponse.json({
    success: true,
    processed: results.length,
    errors: errors.length,
    results,
    errors
  });
}
```

### Recommended Batch Sizes

For optimal performance with ~100 articles per day:

1. **Small Batches (10-20 posts)**:
   - Best for real-time processing
   - Lower memory usage
   - Easier error handling and recovery
   - **Recommended for your use case**

2. **Medium Batches (20-50 posts)**:
   - Good balance of efficiency and reliability
   - Suitable if processing during off-peak hours

3. **Large Batches (50+ posts)**:
   - Risk of timeouts and memory issues
   - Harder to debug failures
   - **Not recommended** for base64 image processing

### Client Implementation for Batching

```typescript
// Process articles in batches of 15
const processBatch = async (articles, batchSize = 15) => {
  const results = [];

  for (let i = 0; i < articles.length; i += batchSize) {
    const batch = articles.slice(i, i + batchSize);

    try {
      const response = await fetch('/api/v1/posts/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AUTH_TOKEN}`,
        },
        body: JSON.stringify({ posts: batch }),
      });

      const result = await response.json();
      results.push(result);

      // Optional: Add delay between batches to prevent overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error(`Batch ${i / batchSize + 1} failed:`, error);
    }
  }

  return results;
};
```

## Conclusion

This unified Post API provides:

1. **Single Endpoint**: One API endpoint handles both single posts and batch requests intelligently
2. **Base64 Upload**: Images converted to base64, sent as JSON, stored in Azure Blob Storage
3. **External URLs**: Images referenced by URL without blob storage
4. **Efficient Batching**: Optimized for processing multiple articles with recommended batch sizes of 10-20 posts
5. **Easy Maintenance**: Single codebase eliminates duplication and simplifies updates

**Recommended approach for 100 daily articles**:
- Use batch requests with 15 posts per request
- Add 1-second delays between batches
- Total processing time: ~7-10 seconds for 100 articles

**API Usage:**
- Single post: Send post object directly to `/api/v1/posts`
- Batch posts: Send `{ posts: [...] }` to `/api/v1/posts`
