# Change Log

## 2025-05-14: Category Pages and Image Fallback System

### Added
- Created category listing page with card-based UI (`src/app/(frontend)/categories/page.tsx`)
- Implemented category detail page with two-column layout (`src/app/(frontend)/categories/[slug]/page.tsx`)
  - Left column (2/3 width): Shows posts related to the category with pagination
  - Right column (1/3 width): Features a sticky panel showing other categories
- Created reusable fallback image system (`src/utilities/getFallbackImage.ts`)
  - Organized image collections by size and purpose (hero, category, thumbnail)
  - Added support for consistent and random image selection
  - Implemented within PostImage component for better UX

### Changed
- Enhanced category UI with modern visual elements:
  - Gradient overlays and backdrop blur effects
  - Animated hover states
  - Responsive layouts for various screen sizes
- Improved PostCard implementation to use the fallback image system (`src/components/Post/PostCard.tsx`)
- Standardized image display patterns across the site
- Fixed Typescript Errors and Warnings

## 2025-05-13: Database Seeding Implementation

### Added
- Created a command-line based seeding system using the Payload API directly
- Implemented seeders for:
  - Categories (pre-defined categories for news posts)
  - Admin user (<EMAIL>)
  - Posts (with sample content)
  - Media (structure created, manual upload required)
- Added a `db:seed` command to package.json
- Created seed-assets directory structure for media files
- Added JSON-based post data import capability

### Issues and Limitations
- Media seeding via automated means is limited by Payload's file upload system in Node.js
- Simple markdown to Lexical conversion instead of more complex HTML conversion

## 2025-05-12: Home Page Layout Enhancements

### Added
- Created a new reusable `PostImage` component that handles fallback images
- Implemented a modular `PostCard` component with multiple variants:
  - `featured`: Full-width hero card with text overlay
  - `overlay`: Card with full-height background image and text overlay
  - `minimal`: Compact card for grid displays
  - `sidebar`: Side-by-side layout with full-height image
  - `default`: Standard card layout

### Changed
- Restructured home page layout with improved sections:
  - First row: 50% for section 1, two 25% columns for sections 2 and 3
  - Second row: Full-width grid for section 4
  - Third row: 2/3 width for section 5, 1/3 width for section 6
- Enhanced cards with hover effects, transitions, and responsive design
- Improved image loading with fallback images
- Added clickable links to post detail pages for all cards
- Refactored code for better maintainability
- Optimized component structure by separating PostCard into its own component

### Fixed
- Fixed TypeScript errors related to post and image types
- Addressed image path issues with correct heroImage references
- Improved handling of missing images with fallback 